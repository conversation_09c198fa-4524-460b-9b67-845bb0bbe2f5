const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');

const prisma = new PrismaClient();

async function main() {
  // Create admin user
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      password: await bcrypt.hash('admin123', 10),
      role: 'ADMIN',
      status: 'ACTIVE'
    }
  });

  // Create regular user
  const regularUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Regular User',
      password: await bcrypt.hash('user123', 10),
      role: 'USER',
      status: 'ACTIVE'
    }
  });

  // Create categories
  const restaurantCategory = await prisma.category.upsert({
    where: { name: 'Restaurants' },
    update: {},
    create: {
      name: 'Restaurants'
    }
  });

  const shoppingCategory = await prisma.category.upsert({
    where: { name: 'Shopping' },
    update: {},
    create: {
      name: 'Shopping'
    }
  });

  // Create tags
  const fastFoodTag = await prisma.tag.upsert({
    where: { name: 'Fast Food' },
    update: {},
    create: {
      name: 'Fast Food'
    }
  });

  const organicTag = await prisma.tag.upsert({
    where: { name: 'Organic' },
    update: {},
    create: {
      name: 'Organic'
    }
  });

  // Create sample listings
  const listing1 = await prisma.listing.create({
    data: {
      title: 'Joe\'s Pizza',
      description: 'Best pizza in town with fresh ingredients',
      address: '123 Main St',
      city: 'New York',
      state: 'NY',
      country: 'USA',
      phone: '555-0123',
      email: '<EMAIL>',
      status: 'PENDING',
      categoryId: restaurantCategory.id,
      userId: regularUser.id,
      tags: {
        connect: [{ id: fastFoodTag.id }]
      }
    }
  });

  const listing2 = await prisma.listing.create({
    data: {
      title: 'Organic Market',
      description: 'Fresh organic produce and groceries',
      address: '456 Oak Ave',
      city: 'Los Angeles',
      state: 'CA',
      country: 'USA',
      phone: '555-0456',
      email: '<EMAIL>',
      status: 'APPROVED',
      categoryId: shoppingCategory.id,
      userId: regularUser.id,
      views: 25,
      tags: {
        connect: [{ id: organicTag.id }]
      }
    }
  });

  // Create notifications
  await prisma.notification.create({
    data: {
      type: 'LISTING_APPROVED',
      title: 'Listing Approved',
      message: 'Your listing "Organic Market" has been approved and is now live!',
      userId: regularUser.id,
      read: false
    }
  });

  await prisma.notification.create({
    data: {
      type: 'LISTING_REJECTED',
      title: 'Listing Needs Review',
      message: 'Your listing "Joe\'s Pizza" needs some updates. Please check the details.',
      userId: regularUser.id,
      read: false
    }
  });

  // Create CMS pages
  await prisma.page.create({
    data: {
      title: 'About Us',
      slug: 'about',
      content: '<h1>About City Directory</h1><p>Welcome to our city directory platform...</p>',
      published: true
    }
  });

  await prisma.page.create({
    data: {
      title: 'Contact Us',
      slug: 'contact',
      content: '<h1>Contact Us</h1><p>Get in touch with us...</p>',
      published: true
    }
  });

  // Create blog posts
  await prisma.blogPost.create({
    data: {
      title: 'Welcome to City Directory',
      slug: 'welcome-to-city-directory',
      content: '<h1>Welcome to City Directory</h1><p>This is our first blog post...</p>',
      excerpt: 'Welcome to our new city directory platform',
      published: true,
      authorId: adminUser.id
    }
  });

  // Create blog tags
  await prisma.blogTag.create({
    data: {
      name: 'News'
    }
  });

  await prisma.blogTag.create({
    data: {
      name: 'Updates'
    }
  });

  console.log('✅ Seed data created successfully!');
  console.log('🔐 Admin login: <EMAIL> / admin123');
  console.log('👤 User login: <EMAIL> / user123');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding data:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
