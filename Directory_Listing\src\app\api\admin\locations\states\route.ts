import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const states = await prisma.state.findMany({
      include: {
        cities: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    return NextResponse.json(states);
  } catch (error) {
    console.error('Error fetching states:', error);
    return NextResponse.json(
      { error: 'Failed to fetch states' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { name, code, lat, lng } = body;

    // Validate required fields
    if (!name || !code || lat === undefined || lng === undefined) {
      return NextResponse.json(
        { error: 'Name, code, lat, and lng are required' },
        { status: 400 }
      );
    }

    // Check if state code already exists
    const existingState = await prisma.state.findUnique({
      where: { code }
    });

    if (existingState) {
      return NextResponse.json(
        { error: 'State code already exists' },
        { status: 400 }
      );
    }

    const state = await prisma.state.create({
      data: {
        name,
        code: code.toUpperCase(),
        lat: parseFloat(lat),
        lng: parseFloat(lng)
      }
    });

    return NextResponse.json(state);
  } catch (error) {
    console.error('Error creating state:', error);
    return NextResponse.json(
      { error: 'Failed to create state' },
      { status: 500 }
    );
  }
}
