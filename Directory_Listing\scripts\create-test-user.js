const { PrismaClient } = require('@prisma/client');
const { hash } = require('bcryptjs');

const prisma = new PrismaClient();

async function createTestUser() {
  try {
    const hashedPassword = await hash('test123', 12);
    
    const user = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Test User',
        password: hashedPassword,
        role: 'USER'
      }
    });

    console.log('✅ Test user created/updated:', user);
    console.log('📧 Email: <EMAIL>');
    console.log('🔐 Password: test123');
  } catch (error) {
    console.error('❌ Error creating test user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createTestUser();
