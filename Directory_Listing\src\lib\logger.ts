// Production-ready logging utility
// Controls console output based on environment

type LogLevel = 'error' | 'warn' | 'info' | 'debug';

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development';
  private isDebugEnabled = process.env.ENABLE_DEBUG_LOGS === 'true';

  private shouldLog(level: LogLevel): boolean {
    if (level === 'error') return true; // Always log errors
    if (level === 'warn') return true; // Always log warnings
    if (level === 'info') return this.isDevelopment || this.isDebugEnabled;
    if (level === 'debug') return this.isDevelopment && this.isDebugEnabled;
    return false;
  }

  error(...args: unknown[]): void {
    if (this.shouldLog('error')) {
      console.error('[ERROR]', ...args);
    }
  }

  warn(...args: unknown[]): void {
    if (this.shouldLog('warn')) {
      console.warn('[WARN]', ...args);
    }
  }

  info(...args: unknown[]): void {
    if (this.shouldLog('info')) {
      console.info('[INFO]', ...args);
    }
  }

  debug(...args: unknown[]): void {
    if (this.shouldLog('debug')) {
      console.log('[DEBUG]', ...args);
    }
  }

  // For geolocation and user-facing messages
  userInfo(...args: unknown[]): void {
    if (this.isDevelopment) {
      console.log('[USER INFO]', ...args);
    }
  }
}

export const logger = new Logger();

// For backward compatibility, keep console methods in development
export const devLog = (...args: unknown[]) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(...args);
  }
};

export const devError = (...args: unknown[]) => {
  if (process.env.NODE_ENV === 'development') {
    console.error(...args);
  }
};
