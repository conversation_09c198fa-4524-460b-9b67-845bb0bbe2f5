import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await req.json();
    const { name, code, lat, lng } = body;

    // Validate required fields
    if (!name || !code || lat === undefined || lng === undefined) {
      return NextResponse.json(
        { error: 'Name, code, lat, and lng are required' },
        { status: 400 }
      );
    }

    // Check if state code already exists for other states
    const existingState = await prisma.state.findFirst({
      where: { 
        code: code.toUpperCase(),
        NOT: { id }
      }
    });

    if (existingState) {
      return NextResponse.json(
        { error: 'State code already exists' },
        { status: 400 }
      );
    }

    const state = await prisma.state.update({
      where: { id },
      data: {
        name,
        code: code.toUpperCase(),
        lat: parseFloat(lat),
        lng: parseFloat(lng)
      }
    });

    return NextResponse.json(state);
  } catch (error) {
    console.error('Error updating state:', error);
    return NextResponse.json(
      { error: 'Failed to update state' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Delete all cities in this state first
    await prisma.city.deleteMany({
      where: { stateId: id }
    });

    // Delete the state
    await prisma.state.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting state:', error);
    return NextResponse.json(
      { error: 'Failed to delete state' },
      { status: 500 }
    );
  }
}
