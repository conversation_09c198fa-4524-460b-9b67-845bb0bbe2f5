"use client";
import { useState } from "react";
import Link from "next/link";

export default function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <>
      {/* Desktop Navigation */}
      <ul className="hidden md:flex gap-8 text-base font-medium">
        <li><Link href="/listings" className="text-slate-700 hover:text-blue-600 transition-colors duration-200">Browse</Link></li>
        <li><Link href="/listings/map" className="text-slate-700 hover:text-blue-600 transition-colors duration-200">Map</Link></li>
        <li><Link href="/events" className="text-slate-700 hover:text-blue-600 transition-colors duration-200">Events</Link></li>
        <li><Link href="/blog" className="text-slate-700 hover:text-blue-600 transition-colors duration-200">Blog</Link></li>
        <li><Link href="/add-listing" className="px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-md hover:shadow-lg">Add Listing</Link></li>
        <li><Link href="/dashboard" className="text-slate-700 hover:text-blue-600 transition-colors duration-200">Dashboard</Link></li>
      </ul>

      {/* Mobile Menu Button */}
      <button
        className="md:hidden p-2 text-slate-700 hover:text-blue-600 transition-colors"
        onClick={() => setIsMenuOpen(!isMenuOpen)}
        aria-label="Toggle menu"
      >
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          {isMenuOpen ? (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          ) : (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          )}
        </svg>
      </button>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="absolute top-full left-0 right-0 bg-white/95 backdrop-blur-md border-t border-slate-200/60 shadow-lg md:hidden">
          <nav className="container mx-auto px-6 py-4">
            <ul className="space-y-4">
              <li><Link href="/listings" className="block text-slate-700 hover:text-blue-600 transition-colors py-2" onClick={() => setIsMenuOpen(false)}>Browse</Link></li>
              <li><Link href="/listings/map" className="block text-slate-700 hover:text-blue-600 transition-colors py-2" onClick={() => setIsMenuOpen(false)}>Map</Link></li>
              <li><Link href="/events" className="block text-slate-700 hover:text-blue-600 transition-colors py-2" onClick={() => setIsMenuOpen(false)}>Events</Link></li>
              <li><Link href="/blog" className="block text-slate-700 hover:text-blue-600 transition-colors py-2" onClick={() => setIsMenuOpen(false)}>Blog</Link></li>
              <li><Link href="/dashboard" className="block text-slate-700 hover:text-blue-600 transition-colors py-2" onClick={() => setIsMenuOpen(false)}>Dashboard</Link></li>
              <li><Link href="/add-listing" className="block w-full text-center px-4 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-md" onClick={() => setIsMenuOpen(false)}>Add Listing</Link></li>
            </ul>
          </nav>
        </div>
      )}
    </>
  );
}
