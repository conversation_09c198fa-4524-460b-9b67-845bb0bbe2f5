const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function seedMapData() {
  console.log('🗺️ Seeding map demo data...');

  try {
    // Create categories if they don't exist
    const restaurantCategory = await prisma.category.upsert({
      where: { name: 'Restaurants' },
      update: {},
      create: { name: 'Restaurants' }
    });

    const shoppingCategory = await prisma.category.upsert({
      where: { name: 'Shopping' },
      update: {},
      create: { name: 'Shopping' }
    });

    const servicesCategory = await prisma.category.upsert({
      where: { name: 'Services' },
      update: {},
      create: { name: 'Services' }
    });

    // Create tags
    const foodTag = await prisma.tag.upsert({
      where: { name: 'Food' },
      update: {},
      create: { name: 'Food' }
    });

    const shoppingTag = await prisma.tag.upsert({
      where: { name: 'Shopping' },
      update: {},
      create: { name: 'Shopping' }
    });

    // Get or create a demo user
    const demoUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        name: 'Demo User',
        email: '<EMAIL>',
        role: 'USER',
        status: 'ACTIVE'
      }
    });

    // Sample listings with real coordinates for major Indian cities
    const sampleListings = [
      {
        title: 'Connaught Place Restaurant',
        description: 'Famous restaurant in the heart of Delhi offering authentic North Indian cuisine',
        address: 'Connaught Place, Block A',
        city: 'New Delhi',
        state: 'Delhi',
        country: 'India',
        phone: '+91-9876543210',
        email: '<EMAIL>',
        locationLat: 28.6315, // Connaught Place coordinates
        locationLng: 77.2167,
        categoryId: restaurantCategory.id,
        userId: demoUser.id,
        status: 'APPROVED'
      },
      {
        title: 'Marine Drive Cafe',
        description: 'Seaside cafe with beautiful views of the Arabian Sea',
        address: 'Marine Drive, Nariman Point',
        city: 'Mumbai',
        state: 'Maharashtra',
        country: 'India',
        phone: '+91-9876543211',
        email: '<EMAIL>',
        locationLat: 18.9436, // Marine Drive coordinates
        locationLng: 72.8236,
        categoryId: restaurantCategory.id,
        userId: demoUser.id,
        status: 'APPROVED'
      },
      {
        title: 'Bangalore Tech Services',
        description: 'IT consulting and software development services',
        address: 'Koramangala, 4th Block',
        city: 'Bangalore',
        state: 'Karnataka',
        country: 'India',
        phone: '+91-9876543212',
        email: '<EMAIL>',
        locationLat: 12.9352, // Koramangala coordinates
        locationLng: 77.6245,
        categoryId: servicesCategory.id,
        userId: demoUser.id,
        status: 'APPROVED'
      },
      {
        title: 'Chennai Silk Emporium',
        description: 'Traditional silk sarees and South Indian textiles',
        address: 'T. Nagar, Ranganathan Street',
        city: 'Chennai',
        state: 'Tamil Nadu',
        country: 'India',
        phone: '+91-9876543213',
        email: '<EMAIL>',
        locationLat: 13.0389, // T. Nagar coordinates
        locationLng: 80.2461,
        categoryId: shoppingCategory.id,
        userId: demoUser.id,
        status: 'APPROVED'
      },
      {
        title: 'Kolkata Sweet House',
        description: 'Famous for rasgulla, sandesh and other Bengali sweets',
        address: 'Park Street',
        city: 'Kolkata',
        state: 'West Bengal',
        country: 'India',
        phone: '+91-9876543214',
        email: '<EMAIL>',
        locationLat: 22.5551, // Park Street coordinates
        locationLng: 88.3534,
        categoryId: restaurantCategory.id,
        userId: demoUser.id,
        status: 'APPROVED'
      },
      {
        title: 'Jaipur Handicrafts',
        description: 'Authentic Rajasthani handicrafts and jewelry',
        address: 'Johari Bazaar',
        city: 'Jaipur',
        state: 'Rajasthan',
        country: 'India',
        phone: '+91-9876543215',
        email: '<EMAIL>',
        locationLat: 26.9225, // Johari Bazaar coordinates
        locationLng: 75.8266,
        categoryId: shoppingCategory.id,
        userId: demoUser.id,
        status: 'APPROVED'
      },
      {
        title: 'Hyderabad Biryani House',
        description: 'Authentic Hyderabadi biryani and Deccan cuisine',
        address: 'Charminar Area',
        city: 'Hyderabad',
        state: 'Telangana',
        country: 'India',
        phone: '+91-9876543216',
        email: '<EMAIL>',
        locationLat: 17.3616, // Charminar coordinates
        locationLng: 78.4747,
        categoryId: restaurantCategory.id,
        userId: demoUser.id,
        status: 'APPROVED'
      },
      {
        title: 'Pune IT Solutions',
        description: 'Software development and digital transformation services',
        address: 'Baner, Pune',
        city: 'Pune',
        state: 'Maharashtra',
        country: 'India',
        phone: '+91-9876543217',
        email: '<EMAIL>',
        locationLat: 18.5679, // Baner coordinates
        locationLng: 73.7811,
        categoryId: servicesCategory.id,
        userId: demoUser.id,
        status: 'APPROVED'
      }
    ];

    // Create listings
    for (const listing of sampleListings) {
      // Check if listing already exists
      const existingListing = await prisma.listing.findFirst({
        where: {
          title: listing.title,
          userId: listing.userId
        }
      });

      if (!existingListing) {
        await prisma.listing.create({
          data: {
            ...listing,
            tags: {
              connect: [{ id: foodTag.id }]
            }
          }
        });
        console.log(`✅ Created listing: ${listing.title}`);
      } else {
        console.log(`⏭️ Listing already exists: ${listing.title}`);
      }
    }

    console.log('✅ Map demo data seeded successfully!');
    console.log(`📍 Created ${sampleListings.length} listings with coordinates`);
    console.log('🗺️ You can now see these on the map!');

  } catch (error) {
    console.error('❌ Error seeding map data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedMapData();
