import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  console.log('🔍 Geocoding API called:', request.url);
  
  const { searchParams } = new URL(request.url);
  const address = searchParams.get('address');
  const lat = searchParams.get('lat');
  const lng = searchParams.get('lng');

  console.log('📍 Parameters:', { address, lat, lng });

  if (!address && (!lat || !lng)) {
    console.log('❌ Missing parameters');
    return NextResponse.json(
      { error: 'Either address or lat/lng parameters are required' },
      { status: 400 }
    );
  }

  try {
    let url: string;
    
    if (address) {
      // Forward geocoding (address to coordinates)
      const encodedAddress = encodeURIComponent(address);
      url = `https://nominatim.openstreetmap.org/search?format=json&limit=1&q=${encodedAddress}&addressdetails=1`;
      console.log('🌐 Nominatim URL:', url);
    } else {
      // Reverse geocoding (coordinates to address)
      url = `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&addressdetails=1`;
      console.log('🌐 Nominatim URL:', url);
    }

    console.log('📡 Making request to Nominatim...');
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'CityDirectory/1.0 (<EMAIL>)'
      }
    });

    console.log('📡 Nominatim response status:', response.status);

    if (!response.ok) {
      console.log('❌ Nominatim API error:', response.statusText);
      throw new Error(`Geocoding API error: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📦 Nominatim data:', data);
    
    if (address) {
      // Forward geocoding response
      if (!data || data.length === 0) {
        console.log('❌ No results found');
        return NextResponse.json({ error: 'No results found' }, { status: 404 });
      }

      const result = data[0];
      const responseData = {
        lat: parseFloat(result.lat),
        lng: parseFloat(result.lon),
        formatted_address: result.display_name || address,
        components: {
          city: result.address?.city || result.address?.town || result.address?.village,
          state: result.address?.state,
          country: result.address?.country,
          postcode: result.address?.postcode
        }
      };
      console.log('✅ Returning geocoding result:', responseData);
      return NextResponse.json(responseData);
    } else {
      // Reverse geocoding response
      if (!data) {
        console.log('❌ No reverse geocoding results found');
        return NextResponse.json({ error: 'No results found' }, { status: 404 });
      }

      const responseData = {
        lat: parseFloat(lat!),
        lng: parseFloat(lng!),
        formatted_address: data.display_name || `${lat}, ${lng}`,
        components: {
          city: data.address?.city || data.address?.town || data.address?.village,
          state: data.address?.state,
          country: data.address?.country,
          postcode: data.address?.postcode
        }
      };
      console.log('✅ Returning reverse geocoding result:', responseData);
      return NextResponse.json(responseData);
    }
  } catch (error) {
    console.error('💥 Geocoding error:', error);
    return NextResponse.json(
      { error: 'Geocoding service unavailable' },
      { status: 500 }
    );
  }
}
