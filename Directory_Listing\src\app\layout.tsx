import type { Metada<PERSON> } from "next";
import { <PERSON>eist, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import SessionProviderWrapper from "@/components/SessionProviderWrapper";
import Navigation from "@/components/Navigation";
import Link from "next/link";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "CityDirectory - Discover India's Best Local Businesses",
  description: "Connect with exceptional businesses across India, discover amazing local services, and explore hidden gems in your city - all in one beautiful platform.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 text-slate-900 min-h-screen`}>
        <SessionProviderWrapper>
          <header className="sticky top-0 z-50 bg-white/95 backdrop-blur-md border-b border-slate-200/60 shadow-sm">
            <nav className="container mx-auto flex items-center justify-between py-4 px-6 lg:px-8 relative">
              <Link href="/" className="text-2xl lg:text-3xl font-bold tracking-tight bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                CityDirectory
              </Link>
              <Navigation />
            </nav>
          </header>
          <main className="min-h-[85vh]">{children}</main>
          <footer className="bg-slate-900 text-slate-100 py-12 mt-16">
            <div className="container mx-auto flex flex-col md:flex-row items-center justify-between gap-6 px-6 lg:px-8">
              <div className="text-xl font-bold bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">CityDirectory</div>
              <div className="flex gap-8 text-sm">
                <Link href="/about" className="hover:text-blue-400 transition-colors">About</Link>
                <Link href="/contact" className="hover:text-blue-400 transition-colors">Contact</Link>
                <Link href="/terms" className="hover:text-blue-400 transition-colors">Terms</Link>
                <Link href="/privacy" className="hover:text-blue-400 transition-colors">Privacy</Link>
              </div>
              <div className="text-xs text-slate-400">&copy; {new Date().getFullYear()} CityDirectory. All rights reserved.</div>
            </div>
          </footer>
        </SessionProviderWrapper>
      </body>
    </html>
  );
}
