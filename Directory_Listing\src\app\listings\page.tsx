"use client";
import { useState, useEffect, Suspense } from "react";
import Link from "next/link";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import dynamic from "next/dynamic";
import LocationIndicator from "@/components/LocationIndicator";
import { LocationService } from "@/services/LocationService";

// Dynamically import MapView to avoid SSR issues
const MapView = dynamic(() => import("@/components/MapView"), {
  ssr: false,
  loading: () => (
    <div className="w-full h-96 bg-gray-100 rounded-lg flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
        <p className="text-gray-600 text-sm">Loading map...</p>
      </div>
    </div>
  ),
});

interface Listing {
  id: string;
  title: string;
  description: string;
  address: string;
  city: string;
  state: string;
  phone?: string;
  email?: string;
  website?: string;
  locationLat?: number;
  locationLng?: number;
  category: {
    id: string;
    name: string;
  };
  tags: Array<{
    id: string;
    name: string;
  }>;
  images: Array<{
    id: string;
    url: string;
  }>;
  user: {
    id: string;
    name?: string;
  };
  createdAt: string;
}

interface Category {
  id: string;
  name: string;
}

interface Tag {
  id: string;
  name: string;
}

export default function ListingsPage() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-pulse">
          <div className="w-8 h-8 bg-blue-500 rounded-full animate-bounce mx-auto mb-4"></div>
          <p className="text-slate-600">Loading listings...</p>
        </div>
      </div>
    </div>}>
      <ListingsPageContent />
    </Suspense>
  );
}

function ListingsPageContent() {
  const [listings, setListings] = useState<Listing[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState("newest");
  const [viewMode, setViewMode] = useState<"grid" | "list" | "map">("grid");
  const [isFullScreenMap, setIsFullScreenMap] = useState(false);
  const [selectedListing, setSelectedListing] = useState<Listing | null>(null);
  const [showAllLocations, setShowAllLocations] = useState(false);

  const searchParams = useSearchParams();
  
  useEffect(() => {
    // Get URL parameters
    const search = searchParams.get("search") || "";
    const category = searchParams.get("category") || "";
    
    setSearchTerm(search);
    setSelectedCategory(category);
    
    fetchData();
  }, [searchParams]);

  // Refetch data when showAllLocations changes
  useEffect(() => {
    fetchData();
  }, [showAllLocations]);

  async function fetchData() {
    try {
      setLoading(true);
      
      // Build API URLs with location filtering
      const searchParams = new URLSearchParams();
      
      // Add search parameter from URL if exists
      const urlSearch = new URLSearchParams(window.location.search).get("search");
      const urlCategory = new URLSearchParams(window.location.search).get("category");
      
      if (urlSearch) searchParams.set('search', urlSearch);
      if (urlCategory) searchParams.set('category', urlCategory);
      
      // Add location parameters only if not showing all locations
      if (!showAllLocations) {
        const locationParams = LocationService.getLocationQueryParams();
        Object.entries(locationParams).forEach(([key, value]) => {
          if (value) searchParams.set(key, value);
        });
      }
      
      const listingsUrl = `/api/listings?${searchParams.toString()}`;
      
      // Fetch all data in parallel
      const [listingsRes, categoriesRes, tagsRes] = await Promise.all([
        fetch(listingsUrl),
        fetch("/api/admin/categories"),
        fetch("/api/admin/tags")
      ]);

      // Handle potential errors
      if (!listingsRes.ok) {
        console.error("Failed to fetch listings:", listingsRes.status);
        setListings([]);
      } else {
        const listingsData = await listingsRes.json();
        setListings(Array.isArray(listingsData) ? listingsData : []);
      }

      if (!categoriesRes.ok) {
        console.error("Failed to fetch categories:", categoriesRes.status);
        setCategories([]);
      } else {
        const categoriesData = await categoriesRes.json();
        setCategories(Array.isArray(categoriesData) ? categoriesData : []);
      }

      if (!tagsRes.ok) {
        console.error("Failed to fetch tags:", tagsRes.status);
        setTags([]);
      } else {
        const tagsData = await tagsRes.json();
        setTags(Array.isArray(tagsData) ? tagsData : []);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      // Set empty arrays to prevent errors
      setListings([]);
      setCategories([]);
      setTags([]);
    } finally {
      setLoading(false);
    }
  }

  // Filter and search logic
  const filteredListings = listings.filter(listing => {
    const matchesSearch = !searchTerm || 
      listing.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      listing.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      listing.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
      listing.category.name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = !selectedCategory || 
      listing.category.name.toLowerCase() === selectedCategory.toLowerCase();
    
    const matchesTags = selectedTags.length === 0 || 
      selectedTags.some(tagId => listing.tags.some(tag => tag.id === tagId));
    
    return matchesSearch && matchesCategory && matchesTags;
  });

  // Sort logic
  const sortedListings = [...filteredListings].sort((a, b) => {
    switch (sortBy) {
      case "newest":
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      case "oldest":
        return new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
      case "name":
        return a.title.localeCompare(b.title);
      case "city":
        return a.city.localeCompare(b.city);
      default:
        return 0;
    }
  });

  const handleTagToggle = (tagId: string) => {
    setSelectedTags(prev => 
      prev.includes(tagId) 
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="container mx-auto px-6 lg:px-8 py-12">
          <div className="animate-pulse">
            <div className="h-8 bg-slate-200 rounded w-64 mb-8"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="bg-white rounded-2xl h-80 shadow-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto px-6 lg:px-8 py-12">
        
        {/* Header */}
        <div className="mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
            Discover Local Businesses
          </h1>
          <p className="text-xl text-slate-600 max-w-2xl mb-4">
            Explore {filteredListings.length} amazing businesses {showAllLocations ? 'across India' : 'in your area'}
          </p>
          
          {/* Location Indicator */}
          <div className="flex items-center justify-between">
            <div className="flex justify-start">
              <LocationIndicator />
            </div>
            
            {/* Show All Locations Toggle */}
            <div className="flex items-center space-x-3">
              <label className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={showAllLocations}
                  onChange={(e) => setShowAllLocations(e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                />
                <span className="text-sm font-medium text-slate-600">
                  Show listings from all locations
                </span>
              </label>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            
            {/* Search Input */}
            <div className="lg:col-span-2">
              <label className="block text-sm font-medium text-slate-700 mb-2">Search</label>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name, description, or city..."
                className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
              />
            </div>

            {/* Category Filter */}
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Category</label>
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                aria-label="Select category"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Sort Options */}
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">Sort By</label>
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                aria-label="Sort options"
              >
                <option value="newest">Newest First</option>
                <option value="oldest">Oldest First</option>
                <option value="name">Name A-Z</option>
                <option value="city">City A-Z</option>
              </select>
            </div>
          </div>

          {/* Tags Filter */}
          {tags.length > 0 && (
            <div className="mt-6">
              <label className="block text-sm font-medium text-slate-700 mb-3">Filter by Tags</label>
              <div className="flex flex-wrap gap-2">
                {tags.map(tag => (
                  <button
                    key={tag.id}
                    onClick={() => handleTagToggle(tag.id)}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                      selectedTags.includes(tag.id)
                        ? "bg-blue-100 text-blue-800 border-2 border-blue-300"
                        : "bg-slate-100 text-slate-700 border-2 border-transparent hover:bg-slate-200"
                    }`}
                  >
                    {tag.name}
                  </button>
                ))}
              </div>
              {selectedTags.length > 0 && (
                <button
                  onClick={() => setSelectedTags([])}
                  className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                >
                  Clear all tags
                </button>
              )}
            </div>
          )}
        </div>

        {/* View Mode Toggle and Results Count */}
        <div className="flex justify-between items-center mb-8">
          <div className="text-slate-600">
            Showing {sortedListings.length} of {listings.length} listings
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => setViewMode("grid")}
              className={`p-2 rounded-lg transition-colors ${viewMode === "grid" ? "bg-blue-100 text-blue-600" : "text-slate-400 hover:text-slate-600"}`}
              aria-label="Grid view"
              title="Grid view"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </button>
            <button
              onClick={() => setViewMode("list")}
              className={`p-2 rounded-lg transition-colors ${viewMode === "list" ? "bg-blue-100 text-blue-600" : "text-slate-400 hover:text-slate-600"}`}
              aria-label="List view"
              title="List view"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
              </svg>
            </button>
            <button
              onClick={() => setViewMode("map")}
              className={`p-2 rounded-lg transition-colors ${viewMode === "map" ? "bg-blue-100 text-blue-600" : "text-slate-400 hover:text-slate-600"}`}
              aria-label="Map view"
              title="Map view"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M12 1.586l-4 4v12.828l4-4V1.586zM3.707 3.293A1 1 0 002 4v10a1 1 0 00.293.707L6 18.414V5.586L3.707 3.293zM17.707 5.293L14 1.586v12.828l2.293 2.293A1 1 0 0018 16V6a1 1 0 00-.293-.707z" clipRule="evenodd" />
              </svg>
            </button>
            {viewMode === "map" && (
              <button
                onClick={() => setIsFullScreenMap(!isFullScreenMap)}
                className={`p-2 rounded-lg transition-colors ${isFullScreenMap ? "bg-green-100 text-green-600" : "text-slate-400 hover:text-slate-600"}`}
                aria-label="Toggle fullscreen map"
                title="Toggle fullscreen"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h4a1 1 0 010 2H6.414l2.293 2.293a1 1 0 11-1.414 1.414L5 6.414V8a1 1 0 01-2 0V4zm9 1a1 1 0 010-2h4a1 1 0 011 1v4a1 1 0 01-2 0V6.414l-2.293 2.293a1 1 0 11-1.414-1.414L13.586 5H12zm-9 7a1 1 0 012 0v1.586l2.293-2.293a1 1 0 111.414 1.414L6.414 15H8a1 1 0 010 2H4a1 1 0 01-1-1v-4zm13-1a1 1 0 011 1v4a1 1 0 01-1 1h-4a1 1 0 010-2h1.586l-2.293-2.293a1 1 0 111.414-1.414L15 13.586V12a1 1 0 011-1z" clipRule="evenodd" />
                </svg>
              </button>
            )}
          </div>
        </div>

        {/* Results */}
        {sortedListings.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-slate-900 mb-2">No listings found</h3>
            <p className="text-slate-600 mb-6">Try adjusting your search criteria or filters</p>
            <button
              onClick={() => {
                setSearchTerm("");
                setSelectedCategory("");
                setSelectedTags([]);
              }}
              className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              Clear All Filters
            </button>
          </div>
        ) : viewMode === "map" ? (
          <div className={`${isFullScreenMap ? "fixed inset-0 z-50 bg-white" : ""}`}>
            {isFullScreenMap && (
              <div className="absolute top-4 right-4 z-10">
                <button
                  onClick={() => setIsFullScreenMap(false)}
                  className="p-2 bg-white rounded-lg shadow-lg hover:bg-gray-50 transition-colors"
                  aria-label="Exit fullscreen"
                >
                  <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            )}
            <MapView
              listings={sortedListings}
              onListingSelect={(listing) => setSelectedListing(listing)}
              className={isFullScreenMap ? "w-full h-full" : "w-full h-96 md:h-[500px]"}
            />
            {selectedListing && !isFullScreenMap && (
              <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="flex items-start gap-4">
                  <img
                    src={selectedListing.images[0]?.url || "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&w=100&q=80"}
                    alt={selectedListing.title}
                    className="w-20 h-20 object-cover rounded-lg"
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold text-gray-900">{selectedListing.title}</h3>
                    <p className="text-sm text-gray-600">{selectedListing.category.name}</p>
                    <p className="text-sm text-gray-500 mt-1">{selectedListing.city}, {selectedListing.state}</p>
                    <Link
                      href={`/listings/${selectedListing.id}`}
                      className="inline-block mt-2 px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      View Details
                    </Link>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className={viewMode === "grid" 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" 
            : "space-y-6"
          }>
            {sortedListings.map((listing, index) => (
              <ListingCard 
                key={listing.id} 
                listing={listing} 
                viewMode={viewMode as "grid" | "list"}
                index={index}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// Listing Card Component
function ListingCard({ listing, viewMode, index }: { listing: Listing; viewMode: "grid" | "list"; index: number }) {
  const primaryImage = listing.images?.[0]?.url || "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&w=600&q=80";
  
  // Calculate delay class based on index
  const delayClass = index < 8 ? `delay-${Math.min(index + 1, 8) * 100}` : '';

  if (viewMode === "list") {
    return (
      <div 
        className={`bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden animate-fade-in ${delayClass}`}
      >
        <div className="md:flex">
          <div className="md:w-64 h-48 md:h-auto relative">
            <Image
              src={primaryImage}
              alt={listing.title}
              fill
              className="object-cover"
            />
          </div>
          <div className="flex-1 p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-xl font-bold text-slate-900 mb-2">{listing.title}</h3>
                <p className="text-blue-600 font-medium">{listing.category.name}</p>
              </div>
              <div className="text-right text-sm text-slate-500">
                <div>{listing.city}, {listing.state}</div>
                <div>{new Date(listing.createdAt).toLocaleDateString()}</div>
              </div>
            </div>
            
            <p className="text-slate-600 mb-4 line-clamp-2">{listing.description}</p>
            
            {listing.tags.length > 0 && (
              <div className="flex flex-wrap gap-1 mb-4">
                {listing.tags.slice(0, 3).map(tag => (
                  <span key={tag.id} className="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded-full">
                    {tag.name}
                  </span>
                ))}
                {listing.tags.length > 3 && (
                  <span className="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded-full">
                    +{listing.tags.length - 3} more
                  </span>
                )}
              </div>
            )}
            
            <div className="flex justify-between items-center">
              <div className="text-sm text-slate-500">
                {listing.address}
              </div>
              <Link
                href={`/listings/${listing.id}`}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                View Details
              </Link>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      className={`bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group animate-fade-in ${delayClass}`}
    >
      <div className="relative h-48 overflow-hidden">
        <Image
          src={primaryImage}
          alt={listing.title}
          fill
          className="object-cover group-hover:scale-110 transition-transform duration-500"
        />
        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-2 py-1 rounded-lg text-sm font-medium text-slate-700">
          {listing.category.name}
        </div>
      </div>
      
      <div className="p-6">
        <h3 className="text-xl font-bold text-slate-900 mb-2 group-hover:text-blue-600 transition-colors">
          {listing.title}
        </h3>
        
        <p className="text-slate-600 mb-3 line-clamp-2">{listing.description}</p>
        
        <div className="text-sm text-slate-500 mb-4">
          📍 {listing.city}, {listing.state}
        </div>
        
        {listing.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {listing.tags.slice(0, 2).map(tag => (
              <span key={tag.id} className="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded-full">
                {tag.name}
              </span>
            ))}
            {listing.tags.length > 2 && (
              <span className="px-2 py-1 bg-slate-100 text-slate-600 text-xs rounded-full">
                +{listing.tags.length - 2}
              </span>
            )}
          </div>
        )}
        
        <Link
          href={`/listings/${listing.id}`}
          className="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold group-hover:translate-x-1 transition-transform duration-200"
        >
          View Details →
        </Link>
      </div>
    </div>
  );
}
