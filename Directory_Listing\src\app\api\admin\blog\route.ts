import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdmin } from "@/lib/auth-middleware";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const slug = searchParams.get('slug');
    const published = searchParams.get('published');
    
    if (slug) {
      // Get specific blog post by slug
      const post = await prisma.blogPost.findUnique({
        where: { slug },
        include: {
          author: {
            select: {
              name: true,
              image: true
            }
          },
          tags: true
        }
      });
      
      if (!post) {
        return NextResponse.json(
          { error: 'Blog post not found' },
          { status: 404 }
        );
      }
      
      return NextResponse.json(post);
    }
    
    // Get all blog posts
    const whereCondition: { published?: boolean } = {};
    if (published === 'true') {
      whereCondition.published = true;
    }
    
    const posts = await prisma.blogPost.findMany({
      where: whereCondition,
      include: {
        author: {
          select: {
            name: true,
            image: true
          }
        },
        tags: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return NextResponse.json(posts);
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const { title, slug, content, excerpt, featured, published, tagIds, authorId } = await req.json();
    
    if (!title || !content || !authorId) {
      return NextResponse.json(
        { error: 'Title, content, and author ID are required' },
        { status: 400 }
      );
    }
    
    // Create slug from title if not provided
    const finalSlug = slug || title.toLowerCase().replace(/[^a-z0-9]+/g, '-');
    
    const post = await prisma.blogPost.create({
      data: {
        title,
        slug: finalSlug,
        content,
        excerpt,
        featured: featured || false,
        published: published || false,
        authorId,
        tags: {
          connect: tagIds ? tagIds.map((id: string) => ({ id })) : []
        }
      },
      include: {
        author: {
          select: {
            name: true,
            image: true
          }
        },
        tags: true
      }
    });
    
    return NextResponse.json(post);
  } catch (error) {
    console.error('Error creating blog post:', error);
    return NextResponse.json(
      { error: 'Failed to create blog post' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const { id, title, slug, content, excerpt, featured, published, tagIds } = await req.json();
    
    if (!id) {
      return NextResponse.json(
        { error: 'Blog post ID is required' },
        { status: 400 }
      );
    }
    
    const post = await prisma.blogPost.update({
      where: { id },
      data: {
        title,
        slug,
        content,
        excerpt,
        featured,
        published,
        tags: {
          set: tagIds ? tagIds.map((id: string) => ({ id })) : []
        }
      },
      include: {
        author: {
          select: {
            name: true,
            image: true
          }
        },
        tags: true
      }
    });
    
    return NextResponse.json(post);
  } catch (error) {
    console.error('Error updating blog post:', error);
    return NextResponse.json(
      { error: 'Failed to update blog post' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Blog post ID is required' },
        { status: 400 }
      );
    }
    
    await prisma.blogPost.delete({
      where: { id }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting blog post:', error);
    return NextResponse.json(
      { error: 'Failed to delete blog post' },
      { status: 500 }
    );
  }
}
