import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdmin } from "@/lib/auth-middleware";

export async function POST(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const { action, listingIds } = await req.json();
    
    if (!action || !listingIds || !Array.isArray(listingIds)) {
      return NextResponse.json(
        { error: 'Action and listing IDs are required' },
        { status: 400 }
      );
    }

    let result;
    
    switch (action) {
      case 'approve':
        result = await prisma.listing.updateMany({
          where: { id: { in: listingIds } },
          data: { status: 'APPROVED' }
        });
        
        // Create notifications for all affected users
        const approvedListings = await prisma.listing.findMany({
          where: { id: { in: listingIds } },
          include: { user: true }
        });
        
        await Promise.all(approvedListings.map(listing => 
          prisma.notification.create({
            data: {
              type: 'LISTING_APPROVED',
              title: 'Listing Approved',
              message: `Your listing "${listing.title}" has been approved!`,
              userId: listing.userId
            }
          })
        ));
        break;
        
      case 'reject':
        result = await prisma.listing.updateMany({
          where: { id: { in: listingIds } },
          data: { status: 'REJECTED' }
        });
        
        // Create notifications for all affected users
        const rejectedListings = await prisma.listing.findMany({
          where: { id: { in: listingIds } },
          include: { user: true }
        });
        
        await Promise.all(rejectedListings.map(listing => 
          prisma.notification.create({
            data: {
              type: 'LISTING_REJECTED',
              title: 'Listing Rejected',
              message: `Your listing "${listing.title}" has been rejected.`,
              userId: listing.userId
            }
          })
        ));
        break;
        
      case 'delete':
        result = await prisma.listing.deleteMany({
          where: { id: { in: listingIds } }
        });
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        );
    }

    return NextResponse.json({ 
      success: true, 
      count: result.count,
      message: `${action} completed for ${result.count} listings` 
    });
  } catch (error) {
    console.error('Error performing bulk action:', error);
    return NextResponse.json(
      { error: 'Failed to perform bulk action' },
      { status: 500 }
    );
  }
}
