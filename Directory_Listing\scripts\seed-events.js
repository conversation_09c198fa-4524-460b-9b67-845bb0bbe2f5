const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const sampleEvents = [
  {
    title: "Tech Conference 2025",
    description: "Join us for the biggest technology conference of the year featuring industry experts, workshops, and networking opportunities. Discover the latest trends in AI, blockchain, and web development.",
    date: new Date("2025-08-15T09:00:00Z"),
    location: "Bangalore International Exhibition Centre",
    state: "Karnataka",
    city: "Bangalore",
    category: "Technology",
    price: 2500,
    image: "https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=800&q=80",
    status: "APPROVED",
    isFeatured: true,
    lat: 12.9716,
    lng: 77.5946
  },
  {
    title: "Food Festival Mumbai",
    description: "A celebration of Mumbai's diverse culinary scene. Sample delicious street food, gourmet dishes, and regional specialties from across India.",
    date: new Date("2025-08-20T11:00:00Z"),
    location: "Juhu Beach",
    state: "Maharashtra",
    city: "Mumbai",
    category: "Food",
    price: null,
    image: "https://images.unsplash.com/photo-1567337710282-00832b415979?q=80&w=1330&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
    status: "APPROVED",
    isFeatured: true,
    lat: 19.0760,
    lng: 72.8777
  },
  {
    title: "Art Exhibition: Modern Masters",
    description: "Explore contemporary art from emerging and established artists. This exhibition showcases paintings, sculptures, and digital art installations.",
    date: new Date("2025-08-25T10:00:00Z"),
    location: "National Gallery of Modern Art",
    state: "Delhi",
    city: "New Delhi",
    category: "Arts",
    price: 500,
    image: "https://images.unsplash.com/photo-1578321272176-b7bbc0679853?w=800&q=80",
    status: "APPROVED",
    isFeatured: false,
    lat: 28.6139,
    lng: 77.2090
  },
  {
    title: "Music Concert: Classical Fusion",
    description: "An evening of beautiful classical music fused with contemporary sounds. Featuring renowned musicians from across the country.",
    date: new Date("2025-09-01T19:00:00Z"),
    location: "Shanmukhananda Hall",
    state: "Maharashtra",
    city: "Mumbai",
    category: "Music",
    price: 1500,
    image: "https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=800&q=80",
    status: "APPROVED",
    isFeatured: false,
    lat: 19.0760,
    lng: 72.8777
  },
  {
    title: "Business Networking Event",
    description: "Connect with fellow entrepreneurs, investors, and business leaders. Great opportunity for partnerships and collaborations.",
    date: new Date("2025-09-05T18:00:00Z"),
    location: "The Leela Palace",
    state: "Karnataka",
    city: "Bangalore",
    category: "Business",
    price: 1000,
    image: "https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=800&q=80",
    status: "PENDING",
    isFeatured: false,
    lat: 12.9716,
    lng: 77.5946
  },
  {
    title: "Marathon for Health",
    description: "Join thousands of runners in this charity marathon supporting healthcare initiatives. All fitness levels welcome!",
    date: new Date("2025-09-10T06:00:00Z"),
    location: "Marine Drive",
    state: "Maharashtra",
    city: "Mumbai",
    category: "Sports",
    price: null,
    image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&q=80",
    status: "APPROVED",
    isFeatured: true,
    lat: 18.9440,
    lng: 72.8242
  }
];

async function seedEvents() {
  try {
    // Get admin user
    const adminUser = await prisma.user.findFirst({
      where: { role: 'ADMIN' }
    });

    if (!adminUser) {
      console.log('No admin user found. Creating one...');
      const admin = await prisma.user.create({
        data: {
          name: 'Admin User',
          email: '<EMAIL>',
          role: 'ADMIN',
          status: 'ACTIVE'
        }
      });
      
      console.log('Created admin user:', admin);
    }

    const user = adminUser || await prisma.user.findFirst({ where: { role: 'ADMIN' } });

    // Get cities for location mapping
    const mumbaiCity = await prisma.city.findFirst({
      where: { name: 'Mumbai' }
    });
    
    const bangaloreCity = await prisma.city.findFirst({
      where: { name: 'Bangalore' }
    });
    
    const delhiCity = await prisma.city.findFirst({
      where: { name: 'New Delhi' }
    });

    // Create events
    for (const eventData of sampleEvents) {
      let cityId = null;
      
      if (eventData.city === 'Mumbai' && mumbaiCity) {
        cityId = mumbaiCity.id;
      } else if (eventData.city === 'Bangalore' && bangaloreCity) {
        cityId = bangaloreCity.id;
      } else if (eventData.city === 'New Delhi' && delhiCity) {
        cityId = delhiCity.id;
      }

      const event = await prisma.event.create({
        data: {
          ...eventData,
          cityId,
          createdById: user.id
        }
      });

      console.log(`Created event: ${event.title}`);

      // Create some sample RSVPs
      const rsvpCount = Math.floor(Math.random() * 50) + 10;
      for (let i = 0; i < rsvpCount; i++) {
        try {
          await prisma.eventRSVP.create({
            data: {
              eventId: event.id,
              userId: user.id,
              status: Math.random() > 0.5 ? 'INTERESTED' : 'GOING'
            }
          });
        } catch (error) {
          // Skip if RSVP already exists
        }
      }
    }

    console.log('Events seeded successfully!');
  } catch (error) {
    console.error('Error seeding events:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedEvents();
