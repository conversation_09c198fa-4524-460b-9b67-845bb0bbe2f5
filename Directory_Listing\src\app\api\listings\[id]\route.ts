import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // Increment view count
    await prisma.listing.update({
      where: { id },
      data: { views: { increment: 1 } }
    });
    
    // Fetch listing details
    const listing = await prisma.listing.findUnique({
      where: { 
        id,
        status: 'APPROVED' // Only show approved listings
      },
      include: {
        category: true,
        tags: true,
        images: true,
        user: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!listing) {
      return NextResponse.json(
        { error: 'Listing not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(listing);
  } catch (error) {
    console.error('Error fetching listing:', error);
    return NextResponse.json(
      { error: 'Failed to fetch listing' },
      { status: 500 }
    );
  }
}
