{"name": "city-directory", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@cloudinary/react": "^1.14.3", "@cloudinary/url-gen": "^1.21.0", "@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.10.1", "@types/leaflet": "^1.9.20", "@types/leaflet.markercluster": "^1.5.5", "@types/react-datepicker": "^7.0.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "framer-motion": "^12.23.12", "leaflet": "^1.9.4", "leaflet.markercluster": "^1.5.3", "lucide-react": "^0.533.0", "next": "15.3.4", "next-auth": "^4.24.11", "nodemailer": "^6.10.1", "prisma": "^6.10.1", "quill": "^2.0.3", "react": "^18.3.1", "react-datepicker": "^8.4.0", "react-dom": "^18.3.1", "react-leaflet": "^4.2.1", "react-quill": "^2.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}