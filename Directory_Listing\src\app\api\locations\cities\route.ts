import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const stateId = searchParams.get('stateId');

    if (!stateId) {
      return NextResponse.json(
        { error: 'State ID is required' },
        { status: 400 }
      );
    }

    const cities = await prisma.city.findMany({
      where: { 
        stateId: stateId,
        active: true 
      },
      include: {
        state: {
          select: {
            name: true,
            code: true
          }
        }
      },
      orderBy: { name: 'asc' }
    });

    return NextResponse.json(cities);
  } catch (error) {
    console.error('Error fetching cities:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cities' },
      { status: 500 }
    );
  }
}
