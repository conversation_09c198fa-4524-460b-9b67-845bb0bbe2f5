"use client";
import { useEffect, useRef, useState } from 'react';
import type { Map, Marker } from 'leaflet';

interface Listing {
  id: string;
  title: string;
  description: string;
  address: string;
  city: string;
  state: string;
  locationLat?: number;
  locationLng?: number;
  category: {
    id: string;
    name: string;
  };
  tags: Array<{
    id: string;
    name: string;
  }>;
  images: Array<{
    id: string;
    url: string;
  }>;
  user: {
    id: string;
    name?: string;
  };
  createdAt: string;
}

interface LeafletMapProps {
  listings: Listing[];
  onListingSelect?: (listing: Listing) => void;
}

export default function LeafletMap({ 
  listings, 
  onListingSelect
}: LeafletMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<Map | null>(null);
  const markersRef = useRef<Marker[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const markerClusterGroupRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize Leaflet map
  useEffect(() => {
    if (!mapRef.current) return;

    const initializeMap = async () => {
      try {
        setIsLoading(true);
        
        // Dynamic imports to avoid SSR issues
        const L = (await import('leaflet')).default;
        
        // Import CSS manually in the component
        if (typeof window !== 'undefined') {
          // Check if CSS is already loaded
          const existingLink = document.querySelector('link[href*="leaflet.css"]');
          if (!existingLink) {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
            link.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
            link.crossOrigin = '';
            document.head.appendChild(link);
          }
          
          // Load marker cluster CSS
          const existingClusterLink = document.querySelector('link[href*="MarkerCluster"]');
          if (!existingClusterLink) {
            const clusterLink = document.createElement('link');
            clusterLink.rel = 'stylesheet';
            clusterLink.href = 'https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.css';
            document.head.appendChild(clusterLink);
            
            const clusterDefaultLink = document.createElement('link');
            clusterDefaultLink.rel = 'stylesheet';
            clusterDefaultLink.href = 'https://unpkg.com/leaflet.markercluster@1.4.1/dist/MarkerCluster.Default.css';
            document.head.appendChild(clusterDefaultLink);
          }
        }
        
        // Import marker clustering
        await import('leaflet.markercluster');

        // Default center (India)
        let center: [number, number] = [20.5937, 78.9629];
        let zoom = 5;

        // Try to get user location
        if (navigator.geolocation) {
          try {
            const position = await new Promise<GeolocationPosition>((resolve, reject) => {
              navigator.geolocation.getCurrentPosition(resolve, reject, {
                timeout: 5000,
                enableHighAccuracy: true
              });
            });
            center = [position.coords.latitude, position.coords.longitude];
            zoom = 12;
          } catch {
            console.log('Geolocation not available, using default center');
          }
        }

        // Create map
        if (!mapRef.current) return;
        
        const map = L.map(mapRef.current, {
          center,
          zoom,
          zoomControl: true,
          scrollWheelZoom: true,
          doubleClickZoom: true,
          touchZoom: true,
          dragging: true
        });

        // Add tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19
        }).addTo(map);

        // Initialize marker cluster group
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const markerClusterGroup = new (L as any).MarkerClusterGroup({
          chunkedLoading: true,
          spiderfyOnMaxZoom: true,
          showCoverageOnHover: false,
          zoomToBoundsOnClick: true,
          maxClusterRadius: 50,
          iconCreateFunction: function(cluster: { getChildCount: () => number }) {
            const count = cluster.getChildCount();
            return L.divIcon({
              html: `
                <div style="
                  width: 40px;
                  height: 40px;
                  background: #1d4ed8;
                  border: 2px solid white;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-size: 12px;
                  font-weight: bold;
                  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
                ">
                  ${count}
                </div>
              `,
              className: 'marker-cluster-custom',
              iconSize: [40, 40]
            });
          }
        });

        mapInstanceRef.current = map;
        markerClusterGroupRef.current = markerClusterGroup;
        map.addLayer(markerClusterGroup);

        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing map:', error);
        setError('Failed to load map. Please check your internet connection.');
        setIsLoading(false);
      }
    };

    initializeMap();

    // Cleanup
    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, []);

  // Update markers when listings change
  useEffect(() => {
    if (!mapInstanceRef.current || !markerClusterGroupRef.current || isLoading) return;

    const updateMarkers = async () => {
      const L = (await import('leaflet')).default;
      
      // Clear existing markers
      markerClusterGroupRef.current.clearLayers();
      markersRef.current = [];

      // Filter listings with valid coordinates
      const validListings = listings.filter(listing => 
        listing.locationLat && listing.locationLng
      );

      if (validListings.length === 0) return;

      // Create custom icon
      const customIcon = L.divIcon({
        html: `
          <div style="
            width: 24px;
            height: 24px;
            background: #2563eb;
            border: 2px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
          ">
            <div style="
              width: 8px;
              height: 8px;
              background: white;
              border-radius: 50%;
            "></div>
          </div>
        `,
        className: 'custom-marker',
        iconSize: [24, 24],
        iconAnchor: [12, 12],
        popupAnchor: [0, -12]
      });

      // Create markers
      const markers = validListings.map(listing => {
        const marker = L.marker(
          [listing.locationLat!, listing.locationLng!],
          { icon: customIcon }
        );

        // Create popup content
        const imageUrl = listing.images[0]?.url || '/placeholder-image.jpg';
        const popupContent = `
          <div style="padding: 12px; max-width: 280px; font-family: system-ui;">
            <div style="display: flex; gap: 12px;">
              <img src="${imageUrl}" alt="${listing.title}" 
                   style="
                     width: 64px; 
                     height: 64px; 
                     object-fit: cover; 
                     border-radius: 8px; 
                     flex-shrink: 0;
                   "
                   onerror="this.src='/placeholder-image.jpg'">
              <div style="flex: 1; min-width: 0;">
                <h3 style="
                  font-size: 14px; 
                  font-weight: 600; 
                  color: #111827; 
                  margin: 0 0 4px 0; 
                  overflow: hidden; 
                  text-overflow: ellipsis; 
                  white-space: nowrap;
                ">${listing.title}</h3>
                <p style="
                  font-size: 12px; 
                  color: #4b5563; 
                  margin: 0 0 4px 0;
                ">${listing.category.name}</p>
                <p style="
                  font-size: 12px; 
                  color: #6b7280; 
                  margin: 0 0 8px 0; 
                  display: -webkit-box; 
                  -webkit-line-clamp: 2; 
                  -webkit-box-orient: vertical; 
                  overflow: hidden;
                ">${listing.description.substring(0, 80)}${listing.description.length > 80 ? '...' : ''}</p>
                <a href="/listings/${listing.id}" 
                   style="
                     display: inline-block; 
                     padding: 4px 8px; 
                     background: #2563eb; 
                     color: white; 
                     font-size: 12px; 
                     text-decoration: none; 
                     border-radius: 4px; 
                     transition: background-color 0.2s;
                   "
                   onmouseover="this.style.background='#1d4ed8'"
                   onmouseout="this.style.background='#2563eb'"
                   target="_blank">
                  View Details
                </a>
              </div>
            </div>
          </div>
        `;

        marker.bindPopup(popupContent, {
          maxWidth: 300,
          className: 'custom-popup'
        });

        // Add click listener
        marker.on('click', () => {
          if (onListingSelect) {
            onListingSelect(listing);
          }
        });

        return marker;
      });

      // Add markers to cluster group
      markerClusterGroupRef.current.addLayers(markers);
      markersRef.current = markers;

      // Fit map to markers if there are multiple
      if (markers.length > 1 && mapInstanceRef.current) {
        const group = L.featureGroup(markers);
        mapInstanceRef.current.fitBounds(group.getBounds(), {
          padding: [20, 20],
          maxZoom: 15
        });
      } else if (markers.length === 1 && mapInstanceRef.current) {
        mapInstanceRef.current.setView(
          [validListings[0].locationLat!, validListings[0].locationLng!], 
          14
        );
      }
    };

    updateMarkers();
  }, [listings, onListingSelect, isLoading]);

  if (error) {
    return (
      <div className="w-full h-full flex items-center justify-center bg-gray-100">
        <div className="text-center">
          <div className="text-red-500 mb-2">
            <svg className="w-12 h-12 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <p className="text-gray-600 text-sm">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full h-full relative">
      {isLoading && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-600 text-sm">Loading map...</p>
          </div>
        </div>
      )}
      <div ref={mapRef} className="w-full h-full" />
      
      {/* Map Attribution */}
      <div className="absolute bottom-2 right-2 bg-white/90 backdrop-blur-sm px-2 py-1 rounded text-xs text-gray-600 z-[1000]">
        Powered by OpenStreetMap
      </div>
    </div>
  );
}
