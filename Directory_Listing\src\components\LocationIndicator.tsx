"use client";
import { useState, useEffect } from "react";
import { LocationService, type UserLocation } from "@/services/LocationService";
import LocationSelector from "./LocationSelector";

interface LocationIndicatorProps {
  onLocationChange?: (location: UserLocation | null) => void;
  className?: string;
}

export default function LocationIndicator({ 
  onLocationChange, 
  className = "" 
}: LocationIndicatorProps) {
  const [currentLocation, setCurrentLocation] = useState<UserLocation | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isInitializing, setIsInitializing] = useState(true);
  const [hasAttemptedDetection, setHasAttemptedDetection] = useState(false);

  useEffect(() => {
    initializeLocation();
  }, []);

  const initializeLocation = async () => {
    setIsInitializing(true);
    
    // First, check if we have stored location
    const stored = LocationService.getStoredLocation();
    if (stored) {
      setCurrentLocation(stored);
      onLocationChange?.(stored);
      setIsInitializing(false);
      return;
    }

    // If no stored location, try auto-detection
    if (!hasAttemptedDetection) {
      setHasAttemptedDetection(true);
      try {
        const detected = await LocationService.detectLocation();
        if (detected) {
          setCurrentLocation(detected);
          onLocationChange?.(detected);
        } else {
          // Auto-detection failed, show manual selector
          setIsModalOpen(true);
        }
      } catch (error) {
        console.error("Auto-detection failed:", error);
        setIsModalOpen(true);
      }
    }
    
    setIsInitializing(false);
  };

  const handleLocationSelect = (location: UserLocation) => {
    setCurrentLocation(location);
    onLocationChange?.(location);
    setIsModalOpen(false);
  };

  const handleChangeLocation = () => {
    setIsModalOpen(true);
  };

  const clearLocation = () => {
    LocationService.clearStoredLocation();
    setCurrentLocation(null);
    onLocationChange?.(null);
    setIsModalOpen(true);
  };

  if (isInitializing) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
        <span className="text-sm text-slate-600">Detecting location...</span>
      </div>
    );
  }

  return (
    <>
      <div className={`flex items-center gap-2 ${className}`}>
        {currentLocation ? (
          <button
            onClick={handleChangeLocation}
            className="flex items-center gap-2 px-3 py-2 bg-blue-50 hover:bg-blue-100 text-blue-700 rounded-lg transition-colors group"
            title="Click to change location"
          >
            <span className="text-blue-600">📍</span>
            <span className="font-medium">
              {LocationService.getLocationDisplay(currentLocation)}
            </span>
            <svg 
              className="w-4 h-4 text-blue-500 group-hover:text-blue-700 transition-colors" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        ) : (
          <button
            onClick={() => setIsModalOpen(true)}
            className="flex items-center gap-2 px-3 py-2 bg-orange-50 hover:bg-orange-100 text-orange-700 rounded-lg transition-colors"
          >
            <span className="text-orange-600">📍</span>
            <span className="font-medium">Select Location</span>
            <svg 
              className="w-4 h-4 text-orange-500" 
              fill="none" 
              stroke="currentColor" 
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        )}

        {/* Quick Actions Dropdown */}
        {currentLocation && (
          <div className="relative group">
            <button
              className="p-1 text-slate-400 hover:text-slate-600 transition-colors"
              title="Location options"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zM12 13a1 1 0 110-2 1 1 0 010 2zM12 20a1 1 0 110-2 1 1 0 010 2z" />
              </svg>
            </button>
            <div className="absolute right-0 top-full mt-1 bg-white rounded-lg shadow-lg border border-slate-200 py-2 min-w-40 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
              <button
                onClick={() => initializeLocation()}
                className="w-full px-4 py-2 text-left text-sm text-slate-700 hover:bg-slate-50 flex items-center gap-2"
              >
                🎯 Auto-detect again
              </button>
              <button
                onClick={clearLocation}
                className="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
              >
                🗑️ Clear location
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Location Selector Modal */}
      <LocationSelector
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onLocationSelect={handleLocationSelect}
        currentLocation={currentLocation}
      />
    </>
  );
}
