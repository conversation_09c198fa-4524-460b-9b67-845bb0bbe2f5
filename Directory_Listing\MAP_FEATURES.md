# Map Integration Features

This document outlines the interactive map functionality added to the CityDirectory application.

## Features Implemented

### 🗺️ Interactive Google Maps Integration
- **Technology**: Google Maps JavaScript API with MarkerClusterer
- **Dynamic Loading**: Client-side rendering to avoid SSR issues
- **Responsive Design**: Works seamlessly on desktop and mobile

### 📍 Location-Based Listings
- **Geocoding**: Automatic address-to-coordinates conversion
- **Manual Coordinates**: Optional manual latitude/longitude input
- **Marker Clustering**: Groups nearby markers for better performance
- **Info Windows**: Rich popups with listing details and images

### 🔄 Multiple View Modes
1. **Grid View**: Traditional card layout
2. **List View**: Detailed horizontal layout  
3. **Map View**: Interactive map with markers
4. **Dedicated Map Page**: Full-screen map experience at `/listings/map`

### 📱 Mobile-Optimized Experience
- **Bottom Sheet**: Mobile-friendly listing details
- **Touch Interactions**: Pan, zoom, tap gestures
- **Fullscreen Toggle**: Immersive map experience
- **Responsive Controls**: Adaptive UI for different screen sizes

### 🎯 Enhanced Filtering
- **Synchronized Filters**: Map respects search, category, and location filters
- **Real-time Updates**: Markers update when filters change
- **URL Parameters**: Deep linking support for search states

## Technical Implementation

### Components Created
- `MapView.tsx` - Main map component with clustering
- `GeocodingService.ts` - Address geocoding utilities
- `google-maps.d.ts` - TypeScript declarations

### Enhanced Forms
- **Add Listing Form**: Auto-geocoding with manual override
- **Admin Forms**: Location coordinate management
- **Validation**: Coordinate range validation

### API Enhancements
- **Listings API**: Location coordinate storage
- **Admin API**: Coordinate management for admins
- **Filtering**: Location-based query support

## Setup Instructions

### 1. Google Maps API Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable the following APIs:
   - Maps JavaScript API
   - Geocoding API
   - Places API (optional, for future features)
4. Create API credentials (API Key)
5. Restrict the API key to your domain

### 2. Environment Configuration
```bash
# Add to your .env.local file
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY="your-api-key-here"
```

### 3. Database Migration
The location fields (`locationLat`, `locationLng`) are already in the schema as optional fields, so no migration is needed.

## Usage Guide

### For Users
1. **Creating Listings**: 
   - Fill in address details
   - Click "Get Coordinates" for automatic geocoding
   - Or manually enter latitude/longitude
   
2. **Browsing Listings**:
   - Toggle between Grid/List/Map views
   - Use fullscreen mode for better map experience
   - Click markers to see listing details

### For Admins
- All listing management includes location coordinate handling
- Bulk operations preserve location data
- Analytics can include location-based insights

## Browser Support
- Modern browsers with ES6+ support
- Mobile Safari, Chrome, Firefox, Edge
- Graceful fallback for browsers without geolocation

## Performance Considerations
- Marker clustering for 100+ listings
- Dynamic imports to reduce initial bundle size
- Lazy loading of map components
- Optimized image loading in info windows

## Future Enhancements
- [ ] Geofenced search (search within radius)
- [ ] Driving directions integration
- [ ] Street View integration
- [ ] Location-based recommendations
- [ ] Offline map caching
- [ ] Advanced clustering with custom icons
- [ ] Heatmap visualization for popular areas

## API Rate Limits
- Google Maps API has usage quotas
- Monitor usage in Google Cloud Console
- Consider implementing request caching for production
- Batch geocoding for bulk imports

## Troubleshooting

### Common Issues
1. **Map not loading**: Check API key and network connectivity
2. **Geocoding failing**: Verify address format and API quotas
3. **Markers not clustering**: Ensure MarkerClusterer library loads
4. **Mobile performance**: Check for memory leaks with large datasets

### Debug Mode
Set `console.log` statements in MapView component to debug:
- Map initialization
- Marker creation
- Clustering behavior
- API responses
