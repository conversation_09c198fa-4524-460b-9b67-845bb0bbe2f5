"use client";
import { useEffect, useState } from "react";

interface User {
  id: string;
  name: string;
  email: string;
  role: 'USER' | 'ADMIN';
  status: 'ACTIVE' | 'BANNED';
  createdAt: string;
  listingCount: number;
}

export default function AdminUsersPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [bulkAction, setBulkAction] = useState("");

  useEffect(() => {
    fetchUsers();
  }, []);

  async function fetchUsers() {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/users");
      const data = await response.json();
      setUsers(data);
    } catch (error) {
      console.error("Error fetching users:", error);
    } finally {
      setLoading(false);
    }
  }

  async function updateUserStatus(userId: string, status: 'ACTIVE' | 'BANNED') {
    setLoading(true);
    try {
      await fetch("/api/admin/users", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ userId, status }),
      });
      fetchUsers();
    } catch (error) {
      console.error("Error updating user status:", error);
    } finally {
      setLoading(false);
    }
  }

  async function updateUserRole(userId: string, role: 'USER' | 'ADMIN') {
    setLoading(true);
    try {
      await fetch("/api/admin/users", {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ userId, role }),
      });
      fetchUsers();
    } catch (error) {
      console.error("Error updating user role:", error);
    } finally {
      setLoading(false);
    }
  }

  function handleBulkSelection(userId: string) {
    if (selectedUsers.includes(userId)) {
      setSelectedUsers(selectedUsers.filter(id => id !== userId));
    } else {
      setSelectedUsers([...selectedUsers, userId]);
    }
  }

  function handleSelectAll() {
    if (selectedUsers.length === users.length) {
      setSelectedUsers([]);
    } else {
      setSelectedUsers(users.map(user => user.id));
    }
  }

  async function handleBulkAction() {
    if (!bulkAction || selectedUsers.length === 0) return;
    
    setLoading(true);
    try {
      for (const userId of selectedUsers) {
        if (bulkAction === "ban") {
          await updateUserStatus(userId, "BANNED");
        } else if (bulkAction === "activate") {
          await updateUserStatus(userId, "ACTIVE");
        }
      }
      setSelectedUsers([]);
      setBulkAction("");
      fetchUsers();
    } catch (error) {
      console.error("Error performing bulk action:", error);
    } finally {
      setLoading(false);
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800';
      case 'BANNED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN': return 'bg-purple-100 text-purple-800';
      case 'USER': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">User Management</h1>
      
      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-600">
              {selectedUsers.length} user{selectedUsers.length > 1 ? 's' : ''} selected
            </span>
            <select 
              value={bulkAction} 
              onChange={(e) => setBulkAction(e.target.value)}
              className="px-3 py-1 border rounded text-sm"
              title="Select bulk action"
            >
              <option value="">Select Action</option>
              <option value="ban">Ban Users</option>
              <option value="activate">Activate Users</option>
            </select>
            <button 
              onClick={handleBulkAction}
              disabled={!bulkAction || loading}
              className="px-4 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
            >
              Apply
            </button>
            <button 
              onClick={() => setSelectedUsers([])}
              className="px-4 py-1 bg-gray-400 text-white rounded text-sm hover:bg-gray-500"
            >
              Clear Selection
            </button>
          </div>
        </div>
      )}

      {/* Users Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="p-4 text-left">
                  <input
                    type="checkbox"
                    checked={selectedUsers.length === users.length && users.length > 0}
                    onChange={handleSelectAll}
                    className="rounded focus:ring-2 focus:ring-blue-500"
                    title="Select all users"
                  />
                </th>
                <th className="p-4 text-left font-medium text-gray-700">User</th>
                <th className="p-4 text-left font-medium text-gray-700">Role</th>
                <th className="p-4 text-left font-medium text-gray-700">Status</th>
                <th className="p-4 text-left font-medium text-gray-700">Listings</th>
                <th className="p-4 text-left font-medium text-gray-700">Joined</th>
                <th className="p-4 text-left font-medium text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {users.map(user => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="p-4">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={() => handleBulkSelection(user.id)}
                      className="rounded focus:ring-2 focus:ring-blue-500"
                      title={`Select ${user.name}`}
                    />
                  </td>
                  <td className="p-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-600">
                          {user.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{user.name}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                      </div>
                    </div>
                  </td>
                  <td className="p-4">
                    <select
                      value={user.role}
                      onChange={(e) => updateUserRole(user.id, e.target.value as 'USER' | 'ADMIN')}
                      className={`text-xs px-3 py-1 rounded-full border-0 focus:ring-2 focus:ring-blue-500 ${getRoleColor(user.role)}`}
                      title="Change role"
                    >
                      <option value="USER">User</option>
                      <option value="ADMIN">Admin</option>
                    </select>
                  </td>
                  <td className="p-4">
                    <select
                      value={user.status}
                      onChange={(e) => updateUserStatus(user.id, e.target.value as 'ACTIVE' | 'BANNED')}
                      className={`text-xs px-3 py-1 rounded-full border-0 focus:ring-2 focus:ring-blue-500 ${getStatusColor(user.status)}`}
                      title="Change status"
                    >
                      <option value="ACTIVE">Active</option>
                      <option value="BANNED">Banned</option>
                    </select>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-gray-900">{user.listingCount || 0}</span>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-gray-500">
                      {new Date(user.createdAt).toLocaleDateString()}
                    </span>
                  </td>
                  <td className="p-4">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => updateUserStatus(user.id, user.status === 'ACTIVE' ? 'BANNED' : 'ACTIVE')}
                        className={`px-3 py-1 rounded text-sm focus:outline-none focus:ring-2 disabled:opacity-50 ${
                          user.status === 'ACTIVE' 
                            ? 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500' 
                            : 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500'
                        }`}
                        disabled={loading}
                      >
                        {user.status === 'ACTIVE' ? 'Ban' : 'Activate'}
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {users.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">No users found</div>
          </div>
        )}
      </div>
    </div>
  );
}
