// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id            String      @id @default(uuid())
  name          String?
  email         String      @unique
  emailVerified DateTime?
  password      String?
  image         String?
  role          Role        @default(USER)
  status        UserStatus  @default(ACTIVE)
  listings      Listing[]
  blogPosts     BlogPost[]
  notifications Notification[]
  events        Event[]
  eventRSVPs    EventRSVP[]
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt
}

enum Role {
  USER
  ADMIN
  MODERATOR
}

enum UserStatus {
  ACTIVE
  BANNED
}

model Listing {
  id          String        @id @default(uuid())
  title       String
  description String
  address     String
  city        String
  state       String
  country     String
  pincode     String?
  phone       String?
  email       String?
  website     String?
  images      Image[]
  locationLat Float?
  locationLng Float?
  views       Int           @default(0)
  status      ListingStatus @default(PENDING)
  category    Category      @relation(fields: [categoryId], references: [id])
  categoryId  String
  tags        Tag[]         @relation("ListingTags")
  user        User          @relation(fields: [userId], references: [id])
  userId      String
  cityRef     City?         @relation(fields: [cityId], references: [id])
  cityId      String?
  stateRef    State?        @relation(fields: [stateId], references: [id])
  stateId     String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
}

enum ListingStatus {
  PENDING
  APPROVED
  REJECTED
}

model Category {
  id        String     @id @default(uuid())
  name      String     @unique
  listings  Listing[]
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
}

model Tag {
  id        String     @id @default(uuid())
  name      String     @unique
  listings  Listing[] @relation("ListingTags")
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
}

model Image {
  id        String   @id @default(uuid())
  url       String
  listing   Listing  @relation(fields: [listingId], references: [id])
  listingId String
  createdAt DateTime @default(now())
}

model BlogPost {
  id        String   @id @default(uuid())
  title     String
  slug      String   @unique
  content   String
  excerpt   String?
  featured  Boolean  @default(false)
  published Boolean  @default(false)
  author    User     @relation(fields: [authorId], references: [id])
  authorId  String
  tags      BlogTag[] @relation("BlogPostTags")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model BlogTag {
  id        String     @id @default(uuid())
  name      String     @unique
  posts     BlogPost[] @relation("BlogPostTags")
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
}

model Page {
  id        String   @id @default(uuid())
  title     String
  slug      String   @unique
  content   String
  published Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model Media {
  id        String    @id @default(uuid())
  filename  String
  url       String
  size      Int
  type      String
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
}

model Notification {
  id        String           @id @default(uuid())
  type      NotificationType
  title     String
  message   String
  read      Boolean          @default(false)
  user      User             @relation(fields: [userId], references: [id])
  userId    String
  createdAt DateTime         @default(now())
  updatedAt DateTime         @updatedAt
}

enum NotificationType {
  LISTING_APPROVED
  LISTING_REJECTED
  GENERAL
}

model State {
  id        String   @id @default(uuid())
  name      String   @unique
  code      String   @unique // State code like "KA", "MH"
  lat       Float
  lng       Float
  active    Boolean  @default(true)
  cities    City[]
  listings  Listing[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

model City {
  id        String   @id @default(uuid())
  name      String
  lat       Float
  lng       Float
  active    Boolean  @default(true)
  state     State    @relation(fields: [stateId], references: [id])
  stateId   String
  listings  Listing[]
  events    Event[]
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([name, stateId])
}

model Event {
  id          String      @id @default(uuid())
  title       String
  description String
  date        DateTime
  location    String
  state       String
  city        String
  category    String
  price       Float?
  image       String?
  isFeatured  Boolean     @default(false)
  status      EventStatus @default(PENDING)
  lat         Float?
  lng         Float?
  cityRef     City?       @relation(fields: [cityId], references: [id])
  cityId      String?
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  createdBy   User        @relation(fields: [createdById], references: [id])
  createdById String
  rsvps       EventRSVP[]
}

enum EventStatus {
  PENDING
  APPROVED
  REJECTED
}

model EventRSVP {
  id      String @id @default(uuid())
  event   Event  @relation(fields: [eventId], references: [id])
  eventId String
  user    User   @relation(fields: [userId], references: [id])
  userId  String
  status  RSVPStatus @default(INTERESTED)
  createdAt DateTime @default(now())
  
  @@unique([eventId, userId])
}

enum RSVPStatus {
  INTERESTED
  GOING
  NOT_GOING
}
