import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdmin } from "@/lib/auth-middleware";
import type { Prisma } from "@prisma/client";

type ListingCreateInput = Omit<Prisma.ListingCreateInput, 'tags'> & {
  tags: {
    connect: { id: string }[];
  };
};

type ListingUpdateInput = Omit<Prisma.ListingUpdateInput, 'tags'> & {
  tags: {
    set: { id: string }[];
  };
};

export async function GET() {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const listings = await prisma.listing.findMany({ 
      include: { 
        category: true, 
        tags: true, 
        images: true, 
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    return NextResponse.json(listings);
  } catch (error) {
    console.error('Error fetching listings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch listings' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const data = await req.json();
    console.log('Creating listing with data:', data);
    
    const { tagIds = [], userId, locationLat, locationLng, ...rest } = data;
    
    // Basic validation
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }
    
    if (!data.title || !data.description || !data.categoryId) {
      return NextResponse.json(
        { error: 'Title, description, and category are required' },
        { status: 400 }
      );
    }

    // Prepare listing data
    const listingData: ListingCreateInput = {
      ...rest,
      userId: userId,
      status: 'APPROVED', // Admin created listings are auto-approved
      tags: {
        connect: tagIds.map((id: string) => ({ id })),
      },
    };

    // Add coordinates if provided
    if (locationLat !== undefined && locationLng !== undefined) {
      listingData.locationLat = parseFloat(locationLat.toString());
      listingData.locationLng = parseFloat(locationLng.toString());
    }

    const listing = await prisma.listing.create({
      data: listingData,
      include: { 
        category: true, 
        tags: true, 
        images: true, 
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
    });
    
    console.log('Created listing:', listing);
    return NextResponse.json(listing);
  } catch (error) {
    console.error('Error creating listing:', error);
    return NextResponse.json(
      { error: 'Failed to create listing' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const data = await req.json();
    const { id, tagIds = [], locationLat, locationLng, ...rest } = data;
    
    if (!id) {
      return NextResponse.json(
        { error: 'Listing ID is required' },
        { status: 400 }
      );
    }

    // Prepare update data
    const updateData: ListingUpdateInput = {
      ...rest,
      tags: {
        set: tagIds.map((id: string) => ({ id })),
      },
    };

    // Add coordinates if provided
    if (locationLat !== undefined && locationLng !== undefined) {
      updateData.locationLat = parseFloat(locationLat.toString());
      updateData.locationLng = parseFloat(locationLng.toString());
    }

    const listing = await prisma.listing.update({
      where: { id },
      data: updateData,
      include: { category: true, tags: true, images: true, user: true },
    });
    return NextResponse.json(listing);
  } catch (error) {
    console.error('Error updating listing:', error);
    return NextResponse.json(
      { error: 'Failed to update listing' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Listing ID is required' },
        { status: 400 }
      );
    }

    await prisma.listing.delete({ where: { id } });
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting listing:', error);
    return NextResponse.json(
      { error: 'Failed to delete listing' },
      { status: 500 }
    );
  }
} 
