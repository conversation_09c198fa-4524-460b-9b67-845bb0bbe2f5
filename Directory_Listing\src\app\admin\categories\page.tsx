"use client";
import { useEffect, useState } from "react";

interface Category {
  id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

export default function AdminCategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [name, setName] = useState("");
  const [loading, setLoading] = useState(false);
  const [editId, setEditId] = useState<string | null>(null);
  const [editName, setEditName] = useState("");
  const [showEdit, setShowEdit] = useState(false);

  useEffect(() => {
    fetchCategories();
  }, []);

  async function fetchCategories() {
    const res = await fetch("/api/admin/categories");
    const data = await res.json();
    setCategories(data);
  }

  async function addCategory(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    await fetch("/api/admin/categories", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ name }),
    });
    setName("");
    setLoading(false);
    fetchCategories();
  }

  function startEdit(cat: Category) {
    setEditId(cat.id);
    setEditName(cat.name);
    setShowEdit(true);
  }

  async function updateCategory(e: React.FormEvent) {
    e.preventDefault();
    if (!editId) return;
    setLoading(true);
    await fetch("/api/admin/categories", {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ id: editId, name: editName }),
    });
    setEditId(null);
    setEditName("");
    setShowEdit(false);
    setLoading(false);
    fetchCategories();
  }

  async function deleteCategory(id: string) {
    setLoading(true);
    await fetch("/api/admin/categories", {
      method: "DELETE",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ id }),
    });
    setLoading(false);
    fetchCategories();
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6 text-gray-900">Manage Categories</h1>
      <form onSubmit={addCategory} className="mb-4 flex gap-2">
        <input
          type="text"
          value={name}
          onChange={e => setName(e.target.value)}
          placeholder="Category name"
          className="px-2 py-1 border rounded text-gray-900"
          required
        />
        <button type="submit" className="px-4 py-2 bg-blue-600 text-white rounded" disabled={loading}>
          Add Category
        </button>
      </form>
      {/* Edit Modal */}
      {showEdit && (
        <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded shadow-lg w-full max-w-sm">
            <h2 className="text-xl font-bold mb-4 text-gray-900">Edit Category</h2>
            <form onSubmit={updateCategory} className="flex gap-2 items-end">
              <input
                type="text"
                value={editName}
                onChange={e => setEditName(e.target.value)}
                placeholder="Category name"
                className="px-2 py-1 border rounded text-gray-900"
                required
              />
              <div className="flex gap-2 mt-2">
                <button type="submit" className="px-4 py-2 bg-green-600 text-white rounded" disabled={loading}>Update</button>
                <button type="button" className="px-4 py-2 bg-gray-400 text-white rounded" onClick={() => { setShowEdit(false); setEditId(null); }}>Cancel</button>
              </div>
            </form>
          </div>
        </div>
      )}
      <table className="w-full bg-white rounded shadow">
        <thead>
          <tr>
            <th className="p-2 text-left font-medium text-gray-900">Name</th>
            <th className="p-2 text-left font-medium text-gray-900">Actions</th>
          </tr>
        </thead>
        <tbody>
          {categories.map(cat => (
            <tr key={cat.id}>
              <td className="p-2 text-gray-900">{cat.name}</td>
              <td className="p-2 space-x-2">
                <button className="px-2 py-1 bg-yellow-400 text-gray-900 rounded" onClick={() => startEdit(cat)} disabled={loading}>Edit</button>
                <button
                  className="px-2 py-1 bg-red-500 text-white rounded"
                  onClick={() => deleteCategory(cat.id)}
                  disabled={loading}
                >
                  Delete
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
} 