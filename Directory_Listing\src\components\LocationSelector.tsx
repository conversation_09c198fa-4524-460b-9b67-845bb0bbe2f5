"use client";
import { useState, useEffect } from "react";
import { LocationService, type UserLocation, type LocationState } from "@/services/LocationService";

interface LocationSelectorProps {
  isOpen: boolean;
  onClose: () => void;
  onLocationSelect: (location: UserLocation) => void;
  currentLocation?: UserLocation | null;
}

export default function LocationSelector({ 
  isOpen, 
  onClose, 
  onLocationSelect, 
  currentLocation 
}: LocationSelectorProps) {
  const [states, setStates] = useState<LocationState[]>([]);
  const [selectedStateId, setSelectedStateId] = useState<string>("");
  const [selectedCityId, setSelectedCityId] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [detecting, setDetecting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadStates();
      if (currentLocation?.stateId) {
        setSelectedStateId(currentLocation.stateId);
      }
      if (currentLocation?.cityId) {
        setSelectedCityId(currentLocation.cityId);
      }
    }
  }, [isOpen, currentLocation]);

  const loadStates = async () => {
    try {
      setLoading(true);
      const statesData = await LocationService.getStates();
      setStates(statesData);
    } catch (error) {
      setError("Failed to load states");
      console.error("Error loading states:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAutoDetect = async () => {
    setDetecting(true);
    setError(null);
    
    try {
      const detectedLocation = await LocationService.detectLocation();
      if (detectedLocation) {
        onLocationSelect(detectedLocation);
        onClose();
      } else {
        setError("Could not detect your location. Please select manually.");
      }
    } catch (error) {
      setError("Failed to detect location. Please select manually.");
      console.error("Location detection error:", error);
    } finally {
      setDetecting(false);
    }
  };

  const handleManualSelect = async () => {
    if (!selectedStateId || !selectedCityId) {
      setError("Please select both state and city");
      return;
    }

    try {
      setLoading(true);
      const location = await LocationService.selectLocation(selectedCityId, selectedStateId);
      if (location) {
        onLocationSelect(location);
        onClose();
      } else {
        setError("Failed to set location");
      }
    } catch (error) {
      setError("Failed to set location");
      console.error("Manual location selection error:", error);
    } finally {
      setLoading(false);
    }
  };

  const selectedState = states.find(state => state.id === selectedStateId);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-slate-900">
              📍 Select Your Location
            </h2>
            <button
              onClick={onClose}
              className="text-slate-400 hover:text-slate-600 text-xl"
            >
              ✕
            </button>
          </div>

          {/* Current Location Display */}
          {currentLocation && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-600 mb-1">Current Location:</p>
              <p className="font-semibold text-blue-900">
                {LocationService.getLocationDisplay(currentLocation)}
              </p>
              <p className="text-xs text-blue-500">
                Method: {currentLocation.detectionMethod}
              </p>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600 text-sm">{error}</p>
            </div>
          )}

          {/* Auto-detect Section */}
          <div className="mb-8">
            <button
              onClick={handleAutoDetect}
              disabled={detecting || loading}
              className="w-full py-3 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 transition-colors"
            >
              {detecting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Detecting Location...
                </>
              ) : (
                <>
                  🎯 Auto-Detect My Location
                </>
              )}
            </button>
            <p className="text-xs text-slate-500 mt-2 text-center">
              Uses your browser&apos;s geolocation to find nearby cities
            </p>
          </div>

          {/* Manual Selection */}
          <div>
            <h3 className="text-lg font-semibold text-slate-900 mb-4">
              Or Select Manually:
            </h3>

            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div className="space-y-4">
                {/* State Selector */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Select State:
                  </label>
                  <select
                    value={selectedStateId}
                    onChange={(e) => {
                      setSelectedStateId(e.target.value);
                      setSelectedCityId(""); // Reset city when state changes
                    }}
                    className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-gray-900"
                    aria-label="Select State"
                    title="Choose your state"
                  >
                    <option value="">Choose a state...</option>
                    {states.map((state) => (
                      <option key={state.id} value={state.id}>
                        {state.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* City Selector */}
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Select City:
                  </label>
                  <select
                    value={selectedCityId}
                    onChange={(e) => setSelectedCityId(e.target.value)}
                    disabled={!selectedStateId}
                    className="w-full p-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-slate-100 disabled:cursor-not-allowed text-gray-900"
                    aria-label="Select City"
                    title="Choose your city"
                  >
                    <option value="">
                      {selectedStateId ? "Choose a city..." : "Select state first"}
                    </option>
                    {selectedState?.cities.map((city) => (
                      <option key={city.id} value={city.id}>
                        {city.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Confirm Button */}
                <button
                  onClick={handleManualSelect}
                  disabled={!selectedStateId || !selectedCityId || loading}
                  className="w-full py-3 px-4 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  ✓ Confirm Location
                </button>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="mt-6 pt-6 border-t border-slate-200">
            <p className="text-xs text-slate-500 text-center">
              Your location helps us show relevant local businesses and listings
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
