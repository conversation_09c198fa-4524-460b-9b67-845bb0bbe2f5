import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

// Calculate distance between two points using Haversine formula
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = toRadians(lat2 - lat1);
  const dLng = toRadians(lng2 - lng1);
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * 
    Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

function toRadians(degrees: number): number {
  return degrees * (Math.PI/180);
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const lat = parseFloat(searchParams.get('lat') || '0');
    const lng = parseFloat(searchParams.get('lng') || '0');

    if (!lat || !lng) {
      return NextResponse.json(
        { error: 'Latitude and longitude are required' },
        { status: 400 }
      );
    }

    // Get all active cities
    const cities = await prisma.city.findMany({
      where: { active: true },
      include: {
        state: {
          select: {
            name: true,
            code: true
          }
        }
      }
    });

    if (cities.length === 0) {
      return NextResponse.json(
        { error: 'No cities found' },
        { status: 404 }
      );
    }

    // Find the nearest city
    let nearestCity = cities[0];
    let minDistance = calculateDistance(lat, lng, nearestCity.lat, nearestCity.lng);

    for (const city of cities.slice(1)) {
      const distance = calculateDistance(lat, lng, city.lat, city.lng);
      if (distance < minDistance) {
        minDistance = distance;
        nearestCity = city;
      }
    }

    // Return the nearest city with distance info
    return NextResponse.json({
      ...nearestCity,
      distance: Math.round(minDistance * 100) / 100 // Round to 2 decimal places
    });

  } catch (error) {
    console.error('Error finding nearest city:', error);
    return NextResponse.json(
      { error: 'Failed to find nearest city' },
      { status: 500 }
    );
  }
}
