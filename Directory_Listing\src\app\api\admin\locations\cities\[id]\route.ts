import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await req.json();
    const { name, stateId, lat, lng } = body;

    // Validate required fields
    if (!name || !stateId || lat === undefined || lng === undefined) {
      return NextResponse.json(
        { error: 'Name, stateId, lat, and lng are required' },
        { status: 400 }
      );
    }

    // Check if state exists
    const state = await prisma.state.findUnique({
      where: { id: stateId }
    });

    if (!state) {
      return NextResponse.json(
        { error: 'State not found' },
        { status: 400 }
      );
    }

    // Check if city already exists in this state (excluding current city)
    const existingCity = await prisma.city.findFirst({
      where: { 
        name: { equals: name },
        stateId,
        NOT: { id }
      }
    });

    if (existingCity) {
      return NextResponse.json(
        { error: 'City already exists in this state' },
        { status: 400 }
      );
    }

    const city = await prisma.city.update({
      where: { id },
      data: {
        name,
        stateId,
        lat: parseFloat(lat),
        lng: parseFloat(lng)
      },
      include: {
        state: {
          select: {
            name: true,
            code: true
          }
        }
      }
    });

    return NextResponse.json(city);
  } catch (error) {
    console.error('Error updating city:', error);
    return NextResponse.json(
      { error: 'Failed to update city' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    await prisma.city.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting city:', error);
    return NextResponse.json(
      { error: 'Failed to delete city' },
      { status: 500 }
    );
  }
}
