import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdmin } from "@/lib/auth-middleware";

export async function POST(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const { listingId, status, reason } = await req.json();
    
    if (!listingId || !status) {
      return NextResponse.json(
        { error: 'Listing ID and status are required' },
        { status: 400 }
      );
    }

    if (!['APPROVED', 'REJECTED', 'PENDING'].includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      );
    }

    // Update listing status
    const listing = await prisma.listing.update({
      where: { id: listingId },
      data: { status },
      include: { 
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      }
    });

    // Create notification for user
    const notificationMessage = status === 'APPROVED' 
      ? `Your listing "${listing.title}" has been approved!`
      : `Your listing "${listing.title}" has been rejected. ${reason ? `Reason: ${reason}` : ''}`;

    await prisma.notification.create({
      data: {
        type: status === 'APPROVED' ? 'LISTING_APPROVED' : 'LISTING_REJECTED',
        title: `Listing ${status.toLowerCase()}`,
        message: notificationMessage,
        userId: listing.userId
      }
    });

    return NextResponse.json({ 
      success: true, 
      listing,
      message: `Listing ${status.toLowerCase()} successfully` 
    });
  } catch (error) {
    console.error('Error approving listing:', error);
    return NextResponse.json(
      { error: 'Failed to update listing status' },
      { status: 500 }
    );
  }
}
