// Geocoding service using our Next.js API route (which proxies to OpenStreetMap Nominatim)
export interface GeocodingResult {
  lat: number;
  lng: number;
  formatted_address: string;
  components: {
    city?: string;
    state?: string;
    country?: string;
    postcode?: string;
  };
}

export class GeocodingService {
  private static readonly API_BASE = '/api/geocoding';

  static async geocodeAddress(address: string): Promise<GeocodingResult | null> {
    try {
      const encodedAddress = encodeURIComponent(address);
      const response = await fetch(`${this.API_BASE}?address=${encodedAddress}`);

      if (!response.ok) {
        if (response.status === 404) {
          return null; // No results found
        }
        throw new Error(`Geocoding request failed: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Geocoding error:', error);
      return null;
    }
  }

  static async reverseGeocode(lat: number, lng: number): Promise<GeocodingResult | null> {
    try {
      const response = await fetch(`${this.API_BASE}?lat=${lat}&lng=${lng}`);

      if (!response.ok) {
        if (response.status === 404) {
          return null; // No results found
        }
        throw new Error(`Reverse geocoding request failed: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      return null;
    }
  }

  static async searchPlaces(query: string): Promise<GeocodingResult[]> {
    try {
      // For place search, we can use the same geocoding endpoint
      // In a more advanced implementation, you might want a separate endpoint
      const result = await this.geocodeAddress(query);
      return result ? [result] : [];
    } catch (error) {
      console.error('Places search error:', error);
      return [];
    }
  }

  // Utility method to validate coordinates
  static isValidCoordinate(lat: number, lng: number): boolean {
    return (
      typeof lat === 'number' && 
      typeof lng === 'number' &&
      lat >= -90 && lat <= 90 &&
      lng >= -180 && lng <= 180 &&
      !isNaN(lat) && !isNaN(lng)
    );
  }

  // Method to get user's current location
  static async getCurrentLocation(): Promise<{ lat: number; lng: number } | null> {
    try {
      if (!navigator.geolocation) {
        throw new Error('Geolocation is not supported');
      }

      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000 // 5 minutes
          }
        );
      });

      return {
        lat: position.coords.latitude,
        lng: position.coords.longitude
      };
    } catch (error) {
      console.error('Error getting current location:', error);
      return null;
    }
  }

  // Helper method to format address for better geocoding results
  static formatAddressForGeocoding(address: string, city?: string, state?: string, country?: string): string {
    let formattedAddress = address.trim();
    
    // Clean up common address formatting issues
    formattedAddress = formattedAddress
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .replace(/,+/g, ',') // Replace multiple commas with single comma
      .replace(/\s*,\s*/g, ', ') // Standardize comma spacing
      .trim();
    
    if (city && !formattedAddress.toLowerCase().includes(city.toLowerCase())) {
      formattedAddress += `, ${city}`;
    }
    
    if (state && !formattedAddress.toLowerCase().includes(state.toLowerCase())) {
      formattedAddress += `, ${state}`;
    }
    
    if (country && !formattedAddress.toLowerCase().includes(country.toLowerCase())) {
      formattedAddress += `, ${country}`;
    }
    
    return formattedAddress;
  }

  // Helper method to create simpler fallback addresses
  static createFallbackAddresses(address: string, city?: string, state?: string, country?: string): string[] {
    const fallbacks: string[] = [];
    
    // Original formatted address
    if (address.trim()) {
      fallbacks.push(this.formatAddressForGeocoding(address, city, state, country));
    }
    
    // City + State + Country
    if (city) {
      if (state && country) {
        fallbacks.push(`${city}, ${state}, ${country}`);
      } else if (country) {
        fallbacks.push(`${city}, ${country}`);
      } else {
        fallbacks.push(city);
      }
    }
    
    // Remove duplicates while preserving order
    return [...new Set(fallbacks)];
  }
}
