import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getAuthSession } from "@/lib/auth-middleware";
import type { Prisma } from "@prisma/client";

type ListingCreateInput = Omit<Prisma.ListingCreateInput, 'tags'> & {
  tags: {
    connect: { id: string }[];
  };
};

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const search = searchParams.get('search') || '';
    const category = searchParams.get('category') || '';
    const city = searchParams.get('city') || '';
    const state = searchParams.get('state') || '';
    const cityId = searchParams.get('cityId') || '';
    const stateId = searchParams.get('stateId') || '';
    
    // Get all approved listings first
    const whereConditions: Prisma.ListingWhereInput & {
      cityId?: string;
      stateId?: string;
    } = {
      status: 'APPROVED'
    };
    
    // Apply location filters at database level when possible
    if (cityId) {
      whereConditions.cityId = cityId;
    } else if (stateId) {
      whereConditions.stateId = stateId;
    } else if (city && !search) {
      whereConditions.city = { contains: city };
    } else if (state && !search) {
      whereConditions.state = { contains: state };
    }
    
    // Apply non-search filters at database level
    if (!search) {
      if (category) {
        whereConditions.category = {
          name: { equals: category }
        };
      }
    }

    const listings = await prisma.listing.findMany({
      where: whereConditions,
      include: {
        category: true,
        tags: true,
        images: true,
        user: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // If search is provided, filter the results in JavaScript for comprehensive search
    if (search) {
      const searchWords = search.toLowerCase().trim().split(/\s+/).filter(word => word.length > 0);
      
      const filteredListings = listings.filter(listing => {
        return searchWords.some(word => {
          return (
            listing.title.toLowerCase().includes(word) ||
            listing.description.toLowerCase().includes(word) ||
            listing.city.toLowerCase().includes(word) ||
            listing.state.toLowerCase().includes(word) ||
            listing.category?.name.toLowerCase().includes(word) ||
            listing.tags.some((tag: { name: string }) => tag.name.toLowerCase().includes(word))
          );
        });
      });
      
      // Also apply location filters to search results if specified
      let locationFilteredListings = filteredListings;
      
      if (city) {
        locationFilteredListings = locationFilteredListings.filter(listing => 
          listing.city.toLowerCase().includes(city.toLowerCase())
        );
      }
      
      if (state) {
        locationFilteredListings = locationFilteredListings.filter(listing => 
          listing.state.toLowerCase().includes(state.toLowerCase())
        );
      }
      
      if (category) {
        locationFilteredListings = locationFilteredListings.filter(listing => 
          listing.category?.name.toLowerCase().includes(category.toLowerCase())
        );
      }
      
      return NextResponse.json(locationFilteredListings);
    }

    return NextResponse.json(listings);
  } catch (error) {
    console.error('Error fetching listings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch listings' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  const session = await getAuthSession();
  
  if (!session?.user?.id) {
    return NextResponse.json(
      { error: 'Unauthorized' },
      { status: 401 }
    );
  }

  try {
    const data = await req.json();
    console.log('Creating user listing with data:', data);
    
    const { tagIds = [], locationLat, locationLng, ...rest } = data;
    
    // Basic validation
    if (!data.title || !data.description || !data.categoryId) {
      return NextResponse.json(
        { error: 'Title, description, and category are required' },
        { status: 400 }
      );
    }

    // Prepare listing data
    const listingData: ListingCreateInput = {
      ...rest,
      userId: session.user.id,
      status: 'PENDING', // User created listings are pending approval
      tags: {
        connect: tagIds.map((id: string) => ({ id })),
      },
    };

    // Add coordinates if provided
    if (locationLat !== undefined && locationLng !== undefined) {
      listingData.locationLat = parseFloat(locationLat.toString());
      listingData.locationLng = parseFloat(locationLng.toString());
    }

    const listing = await prisma.listing.create({
      data: listingData,
      include: { 
        category: true, 
        tags: true, 
        images: true, 
        user: {
          select: {
            id: true,
            name: true,
            email: true
          }
        }
      },
    });
    
    console.log('Created user listing:', listing);
    return NextResponse.json(listing);
  } catch (error) {
    console.error('Error creating user listing:', error);
    return NextResponse.json(
      { error: 'Failed to create listing' },
      { status: 500 }
    );
  }
}
