import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdmin } from "@/lib/auth-middleware";

export async function GET(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const { searchParams } = new URL(req.url);
    const type = searchParams.get('type');
    
    const whereCondition: { type?: { contains: string } } = {};
    if (type) {
      whereCondition.type = { contains: type };
    }
    
    const media = await prisma.media.findMany({
      where: whereCondition,
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    return NextResponse.json(media);
  } catch (error) {
    console.error('Error fetching media:', error);
    return NextResponse.json(
      { error: 'Failed to fetch media' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const { filename, url, size, type } = await req.json();
    
    if (!filename || !url || !size || !type) {
      return NextResponse.json(
        { error: 'Filename, URL, size, and type are required' },
        { status: 400 }
      );
    }
    
    const media = await prisma.media.create({
      data: {
        filename,
        url,
        size,
        type
      }
    });
    
    return NextResponse.json(media);
  } catch (error) {
    console.error('Error creating media:', error);
    return NextResponse.json(
      { error: 'Failed to create media' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const { searchParams } = new URL(req.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Media ID is required' },
        { status: 400 }
      );
    }
    
    await prisma.media.delete({
      where: { id }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting media:', error);
    return NextResponse.json(
      { error: 'Failed to delete media' },
      { status: 500 }
    );
  }
}
