const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seed() {
  try {
    // Create sample categories
    const categories = [
      { name: 'Restaurants' },
      { name: 'Cafes & Bakeries' },
      { name: 'Healthcare' },
      { name: 'Fitness & Yoga' },
      { name: 'Education' },
      { name: 'Retail & Shopping' },
      { name: 'Professional Services' },
      { name: 'Entertainment' },
      { name: 'Travel & Tourism' },
      { name: 'Technology' },
      { name: 'Beauty & Wellness' },
      { name: 'Automotive' },
      { name: 'Real Estate' },
      { name: 'Traditional Crafts' },
      { name: 'Financial Services' }
    ];

    for (const category of categories) {
      await prisma.category.upsert({
        where: { name: category.name },
        update: {},
        create: category
      });
    }

    // Create sample tags
    const tags = [
      { name: 'Vegetarian' },
      { name: 'Vegan' },
      { name: 'Pure Veg' },
      { name: 'Jain Food' },
      { name: 'South Indian' },
      { name: 'North Indian' },
      { name: 'Chinese' },
      { name: 'Continental' },
      { name: 'Street Food' },
      { name: 'Fast Food' },
      { name: 'Home Delivery' },
      { name: 'Takeaway' },
      { name: 'AC' },
      { name: 'Family Restaurant' },
      { name: 'Parking Available' },
      { name: 'WiFi' },
      { name: 'Card Payment' },
      { name: 'UPI Payment' },
      { name: 'Online Booking' },
      { name: '24/7 Service' },
      { name: 'Emergency Service' },
      { name: 'Insurance Accepted' },
      { name: 'Senior Discount' },
      { name: 'Student Discount' },
      { name: 'Wheelchair Accessible' },
      { name: 'Pet Friendly' },
      { name: 'Organic' },
      { name: 'Ayurvedic' },
      { name: 'Yoga Classes' },
      { name: 'Personal Training' }
    ];

    for (const tag of tags) {
      await prisma.tag.upsert({
        where: { name: tag.name },
        update: {},
        create: tag
      });
    }

    console.log('✅ Seed data created successfully!');
    console.log(`Created ${categories.length} categories and ${tags.length} tags`);
  } catch (error) {
    console.error('❌ Error seeding data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seed();
