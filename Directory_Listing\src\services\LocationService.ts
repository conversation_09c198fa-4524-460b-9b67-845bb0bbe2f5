// Location detection and management service
export interface UserLocation {
  city: string;
  state: string;
  cityId?: string;
  stateId?: string;
  lat?: number;
  lng?: number;
  detectionMethod: 'geolocation' | 'manual' | 'stored';
}

export interface LocationState {
  id: string;
  name: string;
  code: string;
  lat: number;
  lng: number;
  cities: LocationCity[];
}

export interface LocationCity {
  id: string;
  name: string;
  lat: number;
  lng: number;
  stateId: string;
}

export class LocationService {
  private static readonly STORAGE_KEY = 'userSelectedLocation';
  private static readonly API_BASE = '/api/locations';

  // Get user's current stored location
  static getStoredLocation(): UserLocation | null {
    if (typeof window === 'undefined') return null;
    
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      return stored ? JSON.parse(stored) : null;
    } catch {
      return null;
    }
  }

  // Store user's selected location
  static storeLocation(location: UserLocation): void {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(location));
    } catch (error) {
      console.error('Failed to store location:', error);
    }
  }

  // Clear stored location
  static clearStoredLocation(): void {
    if (typeof window === 'undefined') return;
    
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.error('Failed to clear stored location:', error);
    }
  }

  // Auto-detect user location using browser geolocation
  static async detectLocation(): Promise<UserLocation | null> {
    return new Promise((resolve) => {
      if (!navigator.geolocation) {
        console.log('Geolocation not supported');
        resolve(null);
        return;
      }

      const timeoutId = setTimeout(() => {
        console.log('Geolocation timeout');
        resolve(null);
      }, 10000); // 10 second timeout

      navigator.geolocation.getCurrentPosition(
        async (position) => {
          clearTimeout(timeoutId);
          try {
            const { latitude, longitude } = position.coords;
            
            // Use reverse geocoding to get city/state
            const response = await fetch(`/api/geocoding?lat=${latitude}&lng=${longitude}`);
            
            if (response.ok) {
              const data = await response.json();
              const location: UserLocation = {
                city: data.components?.city || 'Unknown',
                state: data.components?.state || 'Unknown',
                lat: latitude,
                lng: longitude,
                detectionMethod: 'geolocation'
              };

              // Try to match with our database cities
              const matchedCity = await this.findNearestCity(latitude, longitude);
              if (matchedCity) {
                location.cityId = matchedCity.id;
                location.stateId = matchedCity.stateId;
                location.city = matchedCity.name;
              }

              this.storeLocation(location);
              resolve(location);
            } else {
              resolve(null);
            }
          } catch (error) {
            console.error('Error in reverse geocoding:', error);
            resolve(null);
          }
        },
        (error) => {
          clearTimeout(timeoutId);
          console.error('Geolocation error:', error);
          resolve(null);
        },
        {
          enableHighAccuracy: true,
          timeout: 8000,
          maximumAge: 300000 // 5 minutes cache
        }
      );
    });
  }

  // Find nearest city from our database based on coordinates
  static async findNearestCity(lat: number, lng: number): Promise<LocationCity | null> {
    try {
      const response = await fetch(`${this.API_BASE}/nearest?lat=${lat}&lng=${lng}`);
      if (response.ok) {
        const city = await response.json();
        return city;
      }
    } catch (error) {
      console.error('Error finding nearest city:', error);
    }
    return null;
  }

  // Get all states with their cities
  static async getStates(): Promise<LocationState[]> {
    try {
      const response = await fetch(`${this.API_BASE}/states`);
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error('Error fetching states:', error);
    }
    return [];
  }

  // Get cities for a specific state
  static async getCitiesByState(stateId: string): Promise<LocationCity[]> {
    try {
      const response = await fetch(`${this.API_BASE}/cities?stateId=${stateId}`);
      if (response.ok) {
        return await response.json();
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
    }
    return [];
  }

  // Manual location selection
  static async selectLocation(cityId: string, stateId: string): Promise<UserLocation | null> {
    try {
      const states = await this.getStates();
      const state = states.find(s => s.id === stateId);
      const city = state?.cities.find(c => c.id === cityId);

      if (city && state) {
        const location: UserLocation = {
          city: city.name,
          state: state.name,
          cityId: city.id,
          stateId: state.id,
          lat: city.lat,
          lng: city.lng,
          detectionMethod: 'manual'
        };

        this.storeLocation(location);
        return location;
      }
    } catch (error) {
      console.error('Error selecting location:', error);
    }
    return null;
  }

  // Calculate distance between two points (Haversine formula)
  static calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(lat2 - lat1);
    const dLng = this.toRadians(lng2 - lng1);
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) * 
      Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }

  private static toRadians(degrees: number): number {
    return degrees * (Math.PI/180);
  }

  // Get location display string
  static getLocationDisplay(location: UserLocation | null): string {
    if (!location) return 'Select Location';
    return `${location.city}, ${location.state}`;
  }

  // Check if location is available
  static hasLocation(): boolean {
    return this.getStoredLocation() !== null;
  }

  // Get location query parameters for API calls
  static getLocationQueryParams(): { cityId?: string; stateId?: string; city?: string; state?: string } {
    const location = this.getStoredLocation();
    if (!location) return {};
    
    const params: { cityId?: string; stateId?: string; city?: string; state?: string } = {};
    
    // Prefer cityId/stateId for exact matching
    if (location.cityId) {
      params.cityId = location.cityId;
    } else if (location.city) {
      params.city = location.city;
    }
    
    if (location.stateId) {
      params.stateId = location.stateId;
    } else if (location.state) {
      params.state = location.state;
    }
    
    return params;
  }

  // Build API URL with location parameters
  static buildApiUrl(baseUrl: string, additionalParams: Record<string, string> = {}): string {
    const locationParams = this.getLocationQueryParams();
    const allParams = { ...locationParams, ...additionalParams };
    
    const queryString = Object.entries(allParams)
      .filter(([, value]) => value && value.trim() !== '')
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');
    
    return queryString ? `${baseUrl}?${queryString}` : baseUrl;
  }
}
