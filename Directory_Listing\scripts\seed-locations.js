const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

// Indian States with coordinates (state capitals as center points)
const INDIAN_STATES_DATA = [
  { name: "Andhra Pradesh", code: "AP", lat: 15.9129, lng: 79.7400 },
  { name: "Arunachal Pradesh", code: "AR", lat: 28.2180, lng: 94.7278 },
  { name: "Assam", code: "AS", lat: 26.2006, lng: 92.9376 },
  { name: "Bihar", code: "BR", lat: 25.0961, lng: 85.3131 },
  { name: "Chhattisgarh", code: "CG", lat: 21.2787, lng: 81.8661 },
  { name: "Goa", code: "GA", lat: 15.2993, lng: 74.1240 },
  { name: "Gujarat", code: "GJ", lat: 23.0225, lng: 72.5714 },
  { name: "Haryana", code: "HR", lat: 29.0588, lng: 76.0856 },
  { name: "Himachal Pradesh", code: "HP", lat: 31.1048, lng: 77.1734 },
  { name: "Jharkhand", code: "JH", lat: 23.6102, lng: 85.2799 },
  { name: "Karnataka", code: "KA", lat: 15.3173, lng: 75.7139 },
  { name: "Kerala", code: "KL", lat: 10.8505, lng: 76.2711 },
  { name: "Madhya Pradesh", code: "MP", lat: 22.9734, lng: 78.6569 },
  { name: "Maharashtra", code: "MH", lat: 19.7515, lng: 75.7139 },
  { name: "Manipur", code: "MN", lat: 24.6637, lng: 93.9063 },
  { name: "Meghalaya", code: "ML", lat: 25.4670, lng: 91.3662 },
  { name: "Mizoram", code: "MZ", lat: 23.1645, lng: 92.9376 },
  { name: "Nagaland", code: "NL", lat: 26.1584, lng: 94.5624 },
  { name: "Odisha", code: "OR", lat: 20.9517, lng: 85.0985 },
  { name: "Punjab", code: "PB", lat: 31.1471, lng: 75.3412 },
  { name: "Rajasthan", code: "RJ", lat: 27.0238, lng: 74.2179 },
  { name: "Sikkim", code: "SK", lat: 27.5330, lng: 88.5122 },
  { name: "Tamil Nadu", code: "TN", lat: 11.1271, lng: 78.6569 },
  { name: "Telangana", code: "TG", lat: 18.1124, lng: 79.0193 },
  { name: "Tripura", code: "TR", lat: 23.9408, lng: 91.9882 },
  { name: "Uttar Pradesh", code: "UP", lat: 26.8467, lng: 80.9462 },
  { name: "Uttarakhand", code: "UK", lat: 30.0668, lng: 79.0193 },
  { name: "West Bengal", code: "WB", lat: 22.9868, lng: 87.8550 },
  // Union Territories
  { name: "Delhi", code: "DL", lat: 28.7041, lng: 77.1025 },
  { name: "Puducherry", code: "PY", lat: 11.9416, lng: 79.8083 },
  { name: "Chandigarh", code: "CH", lat: 30.7333, lng: 76.7794 },
  { name: "Andaman and Nicobar Islands", code: "AN", lat: 11.7401, lng: 92.6586 },
  { name: "Dadra and Nagar Haveli and Daman and Diu", code: "DN", lat: 20.1809, lng: 73.0169 },
  { name: "Lakshadweep", code: "LD", lat: 10.5667, lng: 72.6417 },
  { name: "Ladakh", code: "LA", lat: 34.1526, lng: 77.5771 },
  { name: "Jammu and Kashmir", code: "JK", lat: 34.0837, lng: 74.7973 }
];

// Major Indian Cities with their coordinates
const CITIES_DATA = {
  "Maharashtra": [
    { name: "Mumbai", lat: 19.0760, lng: 72.8777 },
    { name: "Pune", lat: 18.5204, lng: 73.8567 },
    { name: "Nagpur", lat: 21.1458, lng: 79.0882 },
    { name: "Nashik", lat: 19.9975, lng: 73.7898 },
    { name: "Aurangabad", lat: 19.8762, lng: 75.3433 },
    { name: "Thane", lat: 19.2183, lng: 72.9781 },
    { name: "Kolhapur", lat: 16.7050, lng: 74.2433 },
    { name: "Solapur", lat: 17.6599, lng: 75.9064 }
  ],
  "Karnataka": [
    { name: "Bengaluru", lat: 12.9716, lng: 77.5946 },
    { name: "Mysuru", lat: 12.2958, lng: 76.6394 },
    { name: "Hubli", lat: 15.3647, lng: 75.1240 },
    { name: "Mangaluru", lat: 12.9141, lng: 74.8560 },
    { name: "Belagavi", lat: 15.8497, lng: 74.4977 },
    { name: "Gulbarga", lat: 17.3297, lng: 76.8343 },
    { name: "Davangere", lat: 14.4644, lng: 75.9217 },
    { name: "Ballari", lat: 15.1394, lng: 76.9214 }
  ],
  "Tamil Nadu": [
    { name: "Chennai", lat: 13.0827, lng: 80.2707 },
    { name: "Coimbatore", lat: 11.0168, lng: 76.9558 },
    { name: "Madurai", lat: 9.9252, lng: 78.1198 },
    { name: "Tiruchirappalli", lat: 10.7905, lng: 78.7047 },
    { name: "Salem", lat: 11.6643, lng: 78.1460 },
    { name: "Tirunelveli", lat: 8.7139, lng: 77.7567 },
    { name: "Erode", lat: 11.3410, lng: 77.7172 },
    { name: "Vellore", lat: 12.9165, lng: 79.1325 }
  ],
  "Delhi": [
    { name: "New Delhi", lat: 28.6139, lng: 77.2090 },
    { name: "Delhi", lat: 28.7041, lng: 77.1025 }
  ],
  "Gujarat": [
    { name: "Ahmedabad", lat: 23.0225, lng: 72.5714 },
    { name: "Surat", lat: 21.1702, lng: 72.8311 },
    { name: "Vadodara", lat: 22.3072, lng: 73.1812 },
    { name: "Rajkot", lat: 22.3039, lng: 70.8022 },
    { name: "Bhavnagar", lat: 21.7645, lng: 72.1519 },
    { name: "Jamnagar", lat: 22.4707, lng: 70.0577 },
    { name: "Gandhinagar", lat: 23.2156, lng: 72.6369 }
  ],
  "West Bengal": [
    { name: "Kolkata", lat: 22.5726, lng: 88.3639 },
    { name: "Howrah", lat: 22.5958, lng: 88.2636 },
    { name: "Durgapur", lat: 23.5204, lng: 87.3119 },
    { name: "Asansol", lat: 23.6739, lng: 86.9524 },
    { name: "Siliguri", lat: 26.7271, lng: 88.3953 }
  ],
  "Rajasthan": [
    { name: "Jaipur", lat: 26.9124, lng: 75.7873 },
    { name: "Jodhpur", lat: 26.2389, lng: 73.0243 },
    { name: "Udaipur", lat: 24.5854, lng: 73.7125 },
    { name: "Kota", lat: 25.2138, lng: 75.8648 },
    { name: "Bikaner", lat: 28.0229, lng: 73.3119 },
    { name: "Ajmer", lat: 26.4499, lng: 74.6399 }
  ],
  "Uttar Pradesh": [
    { name: "Lucknow", lat: 26.8467, lng: 80.9462 },
    { name: "Kanpur", lat: 26.4499, lng: 80.3319 },
    { name: "Ghaziabad", lat: 28.6692, lng: 77.4538 },
    { name: "Agra", lat: 27.1767, lng: 78.0081 },
    { name: "Varanasi", lat: 25.3176, lng: 82.9739 },
    { name: "Allahabad", lat: 25.4358, lng: 81.8463 },
    { name: "Bareilly", lat: 28.3670, lng: 79.4304 },
    { name: "Moradabad", lat: 28.8386, lng: 78.7733 }
  ],
  "Madhya Pradesh": [
    { name: "Bhopal", lat: 23.2599, lng: 77.4126 },
    { name: "Indore", lat: 22.7196, lng: 75.8577 },
    { name: "Gwalior", lat: 26.2183, lng: 78.1828 },
    { name: "Jabalpur", lat: 23.1815, lng: 79.9864 },
    { name: "Ujjain", lat: 23.1765, lng: 75.7885 }
  ],
  "Kerala": [
    { name: "Thiruvananthapuram", lat: 8.5241, lng: 76.9366 },
    { name: "Kochi", lat: 9.9312, lng: 76.2673 },
    { name: "Kozhikode", lat: 11.2588, lng: 75.7804 },
    { name: "Thrissur", lat: 10.5276, lng: 76.2144 },
    { name: "Kollam", lat: 8.8932, lng: 76.6141 },
    { name: "Palakkad", lat: 10.7867, lng: 76.6548 }
  ],
  "Punjab": [
    { name: "Chandigarh", lat: 30.7333, lng: 76.7794 },
    { name: "Ludhiana", lat: 30.9010, lng: 75.8573 },
    { name: "Amritsar", lat: 31.6340, lng: 74.8723 },
    { name: "Jalandhar", lat: 31.3260, lng: 75.5762 },
    { name: "Patiala", lat: 30.3398, lng: 76.3869 }
  ],
  "Haryana": [
    { name: "Faridabad", lat: 28.4089, lng: 77.3178 },
    { name: "Gurgaon", lat: 28.4595, lng: 77.0266 },
    { name: "Panipat", lat: 29.3909, lng: 76.9635 },
    { name: "Ambala", lat: 30.3782, lng: 76.7767 },
    { name: "Yamunanagar", lat: 30.1290, lng: 77.2674 }
  ],
  "Bihar": [
    { name: "Patna", lat: 25.5941, lng: 85.1376 },
    { name: "Gaya", lat: 24.7914, lng: 85.0002 },
    { name: "Bhagalpur", lat: 25.2425, lng: 86.9842 },
    { name: "Muzaffarpur", lat: 26.1209, lng: 85.3647 },
    { name: "Darbhanga", lat: 26.1542, lng: 85.8918 }
  ],
  "Odisha": [
    { name: "Bhubaneswar", lat: 20.2961, lng: 85.8245 },
    { name: "Cuttack", lat: 20.4625, lng: 85.8828 },
    { name: "Rourkela", lat: 22.2604, lng: 84.8536 },
    { name: "Berhampur", lat: 19.3149, lng: 84.7941 }
  ],
  "Assam": [
    { name: "Guwahati", lat: 26.1445, lng: 91.7362 },
    { name: "Silchar", lat: 24.8333, lng: 92.7789 },
    { name: "Dibrugarh", lat: 27.4728, lng: 94.9120 },
    { name: "Jorhat", lat: 26.7509, lng: 94.2037 }
  ],
  "Jharkhand": [
    { name: "Ranchi", lat: 23.3441, lng: 85.3096 },
    { name: "Jamshedpur", lat: 22.8046, lng: 86.2029 },
    { name: "Dhanbad", lat: 23.7957, lng: 86.4304 },
    { name: "Bokaro", lat: 23.6693, lng: 86.1511 }
  ],
  "Himachal Pradesh": [
    { name: "Shimla", lat: 31.1048, lng: 77.1734 },
    { name: "Dharamshala", lat: 32.2190, lng: 76.3234 },
    { name: "Manali", lat: 32.2396, lng: 77.1887 },
    { name: "Solan", lat: 30.9045, lng: 77.0967 }
  ],
  "Uttarakhand": [
    { name: "Dehradun", lat: 30.3165, lng: 78.0322 },
    { name: "Haridwar", lat: 29.9457, lng: 78.1642 },
    { name: "Nainital", lat: 29.3803, lng: 79.4636 },
    { name: "Rishikesh", lat: 30.0869, lng: 78.2676 }
  ],
  "Chhattisgarh": [
    { name: "Raipur", lat: 21.2514, lng: 81.6296 },
    { name: "Bhilai", lat: 21.1938, lng: 81.3509 },
    { name: "Korba", lat: 22.3595, lng: 82.7501 },
    { name: "Bilaspur", lat: 22.0797, lng: 82.1391 }
  ],
  "Goa": [
    { name: "Panaji", lat: 15.4909, lng: 73.8278 },
    { name: "Margao", lat: 15.2700, lng: 73.9500 },
    { name: "Vasco da Gama", lat: 15.3955, lng: 73.8313 }
  ],
  "Andhra Pradesh": [
    { name: "Visakhapatnam", lat: 17.6868, lng: 83.2185 },
    { name: "Vijayawada", lat: 16.5062, lng: 80.6480 },
    { name: "Guntur", lat: 16.3067, lng: 80.4365 },
    { name: "Nellore", lat: 14.4426, lng: 79.9865 },
    { name: "Kurnool", lat: 15.8281, lng: 78.0373 },
    { name: "Rajahmundry", lat: 17.0005, lng: 81.8040 },
    { name: "Tirupati", lat: 13.6288, lng: 79.4192 }
  ],
  "Telangana": [
    { name: "Hyderabad", lat: 17.3850, lng: 78.4867 },
    { name: "Warangal", lat: 17.9689, lng: 79.5941 },
    { name: "Nizamabad", lat: 18.6725, lng: 78.0941 },
    { name: "Karimnagar", lat: 18.4386, lng: 79.1288 }
  ]
};

async function seedLocations() {
  try {
    console.log('🌍 Starting to seed Indian states and cities...');

    // Clear existing data (optional - remove if you want to keep existing data)
    console.log('🗑️ Clearing existing location data...');
    await prisma.city.deleteMany();
    await prisma.state.deleteMany();

    // Seed States
    console.log('🏛️ Seeding states...');
    const statePromises = INDIAN_STATES_DATA.map(async (stateData) => {
      return await prisma.state.create({
        data: stateData
      });
    });

    const createdStates = await Promise.all(statePromises);
    console.log(`✅ Created ${createdStates.length} states`);

    // Create a map of state name to state ID for faster lookup
    const stateMap = new Map();
    createdStates.forEach(state => {
      stateMap.set(state.name, state.id);
    });

    // Seed Cities
    console.log('🏙️ Seeding cities...');
    let totalCities = 0;

    for (const [stateName, cities] of Object.entries(CITIES_DATA)) {
      const stateId = stateMap.get(stateName);
      if (!stateId) {
        console.warn(`⚠️ State ${stateName} not found, skipping cities`);
        continue;
      }

      const cityPromises = cities.map(async (cityData) => {
        return await prisma.city.create({
          data: {
            ...cityData,
            stateId: stateId
          }
        });
      });

      const createdCities = await Promise.all(cityPromises);
      console.log(`✅ Created ${createdCities.length} cities for ${stateName}`);
      totalCities += createdCities.length;
    }

    console.log(`🎉 Successfully seeded ${createdStates.length} states and ${totalCities} cities!`);
    
    // Display summary
    const stateCount = await prisma.state.count();
    const cityCount = await prisma.city.count();
    console.log(`📊 Database now contains: ${stateCount} states and ${cityCount} cities`);

  } catch (error) {
    console.error('❌ Error seeding locations:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedLocations();
