"use client";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  Calendar, 
  MapPin, 
  Users, 
  DollarSign,  
  Share2, 
  ArrowLeft,
  User,
  Edit,
  Trash2
} from "lucide-react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { Event, EventRSVP } from "@/types/events";

interface EventDetailPageProps {
  params: Promise<{ id: string }>;
}

export default function EventDetailPage({ params }: EventDetailPageProps) {
  const { data: session } = useSession();
  const router = useRouter();
  const [event, setEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [userRSVP, setUserRSVP] = useState<string | null>(null);
  const [resolvedParams, setResolvedParams] = useState<{ id: string } | null>(null);

  useEffect(() => {
    params.then(setResolvedParams);
  }, [params]);

  useEffect(() => {
    if (resolvedParams?.id) {
      fetchEvent();
    }
  }, [resolvedParams]);

  const fetchEvent = async () => {
    if (!resolvedParams?.id) return;
    
    try {
      const response = await fetch(`/api/events/${resolvedParams.id}`);
      if (response.ok) {
        const data = await response.json();
        setEvent(data);
        
        // Check if current user has RSVP'd
        if (session?.user) {
          const currentUserRSVP = data.rsvps?.find(
            (rsvp: EventRSVP) => rsvp.user.id === session.user.id
          );
          setUserRSVP(currentUserRSVP?.status || null);
        }
      } else if (response.status === 404) {
        router.push('/events');
      }
    } catch (error) {
      console.error('Error fetching event:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRSVP = async (status: string) => {
    if (!session) {
      router.push('/signup');
      return;
    }

    if (!resolvedParams?.id) return;

    try {
      const response = await fetch(`/api/events/${resolvedParams.id}/rsvp`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });

      if (response.ok) {
        setUserRSVP(status);
        fetchEvent(); // Refresh to get updated RSVP count
      }
    } catch (error) {
      console.error('Error creating RSVP:', error);
    }
  };

  const handleShare = async () => {
    if (!event) return;

    if (navigator.share) {
      try {
        await navigator.share({
          title: event.title,
          text: event.description,
          url: window.location.href
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      navigator.clipboard.writeText(window.location.href);
      alert('Event URL copied to clipboard!');
    }
  };

  const handleDelete = async () => {
    if (!event || !resolvedParams?.id) return;
    
    if (confirm('Are you sure you want to delete this event?')) {
      try {
        const response = await fetch(`/api/events/${resolvedParams.id}`, {
          method: 'DELETE'
        });

        if (response.ok) {
          router.push('/events');
        }
      } catch (error) {
        console.error('Error deleting event:', error);
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (!event) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Event not found</h1>
          <Link href="/events">
            <button className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl">
              Back to Events
            </button>
          </Link>
        </div>
      </div>
    );
  }

  const isOwner = session?.user && event.createdBy.id === session.user.id;
  const isAdmin = session?.user?.role === 'ADMIN';
  const canEdit = isOwner || isAdmin;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      
      {/* Hero Section */}
      <div className="relative">
        {event.image ? (
          <div className="relative h-96 w-full">
            <Image
              src={event.image}
              alt={event.title}
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
          </div>
        ) : (
          <div className="h-96 bg-gradient-to-r from-purple-600 via-blue-600 to-teal-500 relative">
            <div className="absolute inset-0 bg-black/20"></div>
          </div>
        )}
        
        {/* Back Button */}
        <Link href="/events">
          <motion.button 
            whileHover={{ scale: 1.05 }}
            className="absolute top-6 left-6 p-3 bg-white/20 backdrop-blur-lg text-white rounded-full hover:bg-white/30 transition-all duration-300"
          >
            <ArrowLeft className="h-6 w-6" />
          </motion.button>
        </Link>

        {/* Edit/Delete Buttons */}
        {canEdit && (
          <div className="absolute top-6 right-6 flex gap-2">
            <Link href={`/events/${event.id}/edit`}>
              <motion.button 
                whileHover={{ scale: 1.05 }}
                className="p-3 bg-white/20 backdrop-blur-lg text-white rounded-full hover:bg-white/30 transition-all duration-300"
              >
                <Edit className="h-5 w-5" />
              </motion.button>
            </Link>
            <motion.button 
              whileHover={{ scale: 1.05 }}
              onClick={handleDelete}
              className="p-3 bg-red-500/20 backdrop-blur-lg text-white rounded-full hover:bg-red-500/30 transition-all duration-300"
            >
              <Trash2 className="h-5 w-5" />
            </motion.button>
          </div>
        )}

        {/* Event Title Overlay */}
        <div className="absolute bottom-0 left-0 right-0 p-8 text-white">
          <div className="container mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <div className="flex items-center gap-4 mb-4">
                <span className="px-4 py-2 bg-white/20 backdrop-blur-lg rounded-full text-sm font-medium">
                  {event.category}
                </span>
                {event.status === 'PENDING' && (
                  <span className="px-4 py-2 bg-yellow-500/80 backdrop-blur-lg rounded-full text-sm font-medium">
                    Pending Approval
                  </span>
                )}
              </div>
              <h1 className="text-4xl md:text-6xl font-bold mb-4">{event.title}</h1>
              <div className="flex items-center gap-6 text-lg">
                <div className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  {new Date(event.date).toLocaleDateString('en-US', {
                    weekday: 'long',
                    month: 'long',
                    day: 'numeric',
                    year: 'numeric'
                  })}
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  {event.city}, {event.state}
                </div>
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {event._count.rsvps} interested
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-12">
        <div className="grid lg:grid-cols-3 gap-12">
          
          {/* Main Content */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="bg-white/70 backdrop-blur-lg rounded-2xl p-8 border border-white/30 shadow-lg mb-8"
            >
              <h2 className="text-2xl font-bold text-gray-900 mb-6">About This Event</h2>
              <div className="prose prose-lg max-w-none text-gray-700">
                <p>{event.description}</p>
              </div>
            </motion.div>

            {/* Event Details */}
            <motion.div
              initial={{ opacity: 0, y: 50 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="bg-white/70 backdrop-blur-lg rounded-2xl p-8 border border-white/30 shadow-lg mb-8"
            >
              <h3 className="text-xl font-bold text-gray-900 mb-6">Event Details</h3>
              <div className="grid md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Calendar className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Date & Time</div>
                      <div className="text-gray-600">
                        {new Date(event.date).toLocaleDateString('en-US', {
                          weekday: 'long',
                          month: 'long',
                          day: 'numeric',
                          year: 'numeric'
                        })}
                      </div>
                      <div className="text-gray-600">
                        {new Date(event.date).toLocaleTimeString('en-US', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <MapPin className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Location</div>
                      <div className="text-gray-600">{event.location}</div>
                      <div className="text-gray-600">{event.city}, {event.state}</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <DollarSign className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Price</div>
                      <div className="text-gray-600">
                        {event.price ? `₹${event.price}` : 'Free'}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <User className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">Organized by</div>
                      <div className="text-gray-600">{event.createdBy.name}</div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* RSVPs */}
            {event.rsvps && event.rsvps.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6, duration: 0.6 }}
                className="bg-white/70 backdrop-blur-lg rounded-2xl p-8 border border-white/30 shadow-lg"
              >
                <h3 className="text-xl font-bold text-gray-900 mb-6">
                  People Interested ({event._count.rsvps})
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {event.rsvps?.slice(0, 12).map((rsvp) => (
                    <div key={rsvp.id} className="flex items-center gap-3 p-3 bg-white/50 rounded-xl">
                      {rsvp.user.image ? (
                        <Image
                          src={rsvp.user.image}
                          alt={rsvp.user.name}
                          width={40}
                          height={40}
                          className="rounded-full"
                        />
                      ) : (
                        <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-blue-400 rounded-full flex items-center justify-center text-white font-medium">
                          {rsvp.user.name.charAt(0)}
                        </div>
                      )}
                      <div>
                        <div className="font-medium text-gray-900 text-sm">{rsvp.user.name}</div>
                        <div className="text-xs text-gray-500">
                          {rsvp.status === 'GOING' ? '✅ Going' : '❤️ Interested'}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                {event.rsvps && event.rsvps.length > 12 && (
                  <div className="mt-4 text-center text-gray-500">
                    +{event.rsvps.length - 12} more people interested
                  </div>
                )}
              </motion.div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
              className="bg-white/70 backdrop-blur-lg rounded-2xl p-8 border border-white/30 shadow-lg sticky top-8"
            >
              <div className="text-center mb-8">
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {event.price ? `₹${event.price}` : 'Free'}
                </div>
                <div className="text-gray-600">
                  {event.price ? 'per person' : 'event'}
                </div>
              </div>

              {/* RSVP Buttons */}
              <div className="space-y-4 mb-8">
                {session ? (
                  <>
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleRSVP('GOING')}
                      className={`w-full py-4 font-semibold rounded-xl transition-all duration-300 ${
                        userRSVP === 'GOING'
                          ? 'bg-gradient-to-r from-green-500 to-emerald-500 text-white shadow-lg'
                          : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg hover:shadow-xl'
                      }`}
                    >
                      {userRSVP === 'GOING' ? '✅ You\'re Going!' : '🎉 I\'m Going'}
                    </motion.button>
                    
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      onClick={() => handleRSVP('INTERESTED')}
                      className={`w-full py-4 font-semibold rounded-xl transition-all duration-300 ${
                        userRSVP === 'INTERESTED'
                          ? 'bg-gradient-to-r from-pink-500 to-red-500 text-white shadow-lg'
                          : 'border-2 border-purple-600 text-purple-600 hover:bg-purple-50'
                      }`}
                    >
                      {userRSVP === 'INTERESTED' ? '❤️ You\'re Interested' : '❤️ Interested'}
                    </motion.button>
                  </>
                ) : (
                  <Link href="/signup">
                    <motion.button
                      whileHover={{ scale: 1.05 }}
                      className="w-full py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      Sign In to RSVP
                    </motion.button>
                  </Link>
                )}

                <motion.button
                  whileHover={{ scale: 1.05 }}
                  onClick={handleShare}
                  className="w-full py-4 bg-gray-100 text-gray-700 font-semibold rounded-xl hover:bg-gray-200 transition-all duration-300 flex items-center justify-center gap-2"
                >
                  <Share2 className="h-5 w-5" />
                  Share Event
                </motion.button>
              </div>

              {/* Quick Stats */}
              <div className="border-t border-gray-200 pt-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Category</span>
                    <span className="font-medium text-gray-900">{event.category}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Interested</span>
                    <span className="font-medium text-gray-900">{event._count.rsvps} people</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-gray-600">Status</span>
                    <span className={`font-medium ${
                      event.status === 'APPROVED' ? 'text-green-600' : 'text-yellow-600'
                    }`}>
                      {event.status === 'APPROVED' ? 'Live' : 'Pending'}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
