export interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  location: string;
  city: string;
  state: string;
  category: string;
  price?: number;
  image?: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  isFeatured: boolean;
  createdBy: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  cityRef?: {
    name: string;
    state: {
      name: string;
      code: string;
    };
  };
  lat?: number;
  lng?: number;
  _count: {
    rsvps: number;
  };
  rsvps?: EventRSVP[];
  createdAt: string;
}

export interface EventRSVP {
  id: string;
  status: 'GOING' | 'INTERESTED' | 'NOT_GOING';
  eventId: string;
  userId: string;
  user: {
    id: string;
    name: string;
    email: string;
    image?: string;
  };
  createdAt: string;
}

export interface EventFormData {
  title: string;
  description: string;
  date: string;
  location: string;
  category: string;
  price?: number;
  image?: string;
  lat?: number;
  lng?: number;
}
