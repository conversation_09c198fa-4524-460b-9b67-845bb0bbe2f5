import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    const cities = await prisma.city.findMany({
      include: {
        state: {
          select: {
            name: true,
            code: true
          }
        }
      },
      orderBy: [
        { state: { name: 'asc' } },
        { name: 'asc' }
      ]
    });

    return NextResponse.json(cities);
  } catch (error) {
    console.error('Error fetching cities:', error);
    return NextResponse.json(
      { error: 'Failed to fetch cities' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { name, stateId, lat, lng } = body;

    // Validate required fields
    if (!name || !stateId || lat === undefined || lng === undefined) {
      return NextResponse.json(
        { error: 'Name, stateId, lat, and lng are required' },
        { status: 400 }
      );
    }

    // Check if state exists
    const state = await prisma.state.findUnique({
      where: { id: stateId }
    });

    if (!state) {
      return NextResponse.json(
        { error: 'State not found' },
        { status: 400 }
      );
    }

    // Check if city already exists in this state
    const existingCity = await prisma.city.findFirst({
      where: { 
        name: { equals: name },
        stateId
      }
    });

    if (existingCity) {
      return NextResponse.json(
        { error: 'City already exists in this state' },
        { status: 400 }
      );
    }

    const city = await prisma.city.create({
      data: {
        name,
        stateId,
        lat: parseFloat(lat),
        lng: parseFloat(lng)
      },
      include: {
        state: {
          select: {
            name: true,
            code: true
          }
        }
      }
    });

    return NextResponse.json(city);
  } catch (error) {
    console.error('Error creating city:', error);
    return NextResponse.json(
      { error: 'Failed to create city' },
      { status: 500 }
    );
  }
}
