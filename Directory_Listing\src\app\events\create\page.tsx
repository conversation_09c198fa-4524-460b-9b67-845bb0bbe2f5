"use client";
import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { 
  Calendar, 
  MapPin,  
  ArrowLeft,
  Clock,
  Image as ImageIcon
} from "lucide-react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import dynamic from "next/dynamic";

// Dynamically import map picker
const LocationPickerMap = dynamic(() => import("@/components/LocationPickerMap"), { ssr: false });

interface State {
  id: string;
  name: string;
  code: string;
  lat: number;
  lng: number;
}

interface City {
  id: string;
  name: string;
  stateId: string;
  lat: number;
  lng: number;
}

const categories = [
  "Technology", "Business", "Arts", "Music", "Sports", "Food", 
  "Education", "Health", "Entertainment", "Fashion", "Travel"
];

export default function CreateEventPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [states, setStates] = useState<State[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    date: new Date(),
    location: "",
    state: "",
    city: "",
    category: "",
    price: "",
    image: ""
  });
  
  const [coordinates, setCoordinates] = useState<{ lat: number; lng: number } | null>(null);

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/signup");
    }
  }, [status, router]);

  useEffect(() => {
    fetchStates();
    fetchCities();
  }, []);

  const fetchStates = async () => {
    try {
      const response = await fetch('/api/admin/locations/states');
      if (response.ok) {
        const data = await response.json();
        setStates(data);
      }
    } catch (error) {
      console.error('Error fetching states:', error);
    }
  };

  const fetchCities = async () => {
    try {
      const response = await fetch('/api/admin/locations/cities');
      if (response.ok) {
        const data = await response.json();
        setCities(data);
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
    }
  };

  const filteredCities = formData.state 
    ? cities.filter(city => city.stateId === formData.state)
    : cities;

  const handleStateChange = (stateId: string) => {
    const selectedState = states.find(s => s.id === stateId);
    if (selectedState) {
      setFormData(prev => ({
        ...prev,
        state: stateId,
        city: ""
      }));
      setCoordinates({ lat: selectedState.lat, lng: selectedState.lng });
    }
  };

  const handleCityChange = (cityId: string) => {
    const selectedCity = cities.find(c => c.id === cityId);
    if (selectedCity) {
      setFormData(prev => ({
        ...prev,
        city: cityId
      }));
      setCoordinates({ lat: selectedCity.lat, lng: selectedCity.lng });
    }
  };

  const handleLocationSelect = (lat: number, lng: number) => {
    setCoordinates({ lat, lng });
    setShowLocationPicker(false);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!session) {
      router.push("/signup");
      return;
    }

    setLoading(true);

    try {
      const selectedState = states.find(s => s.id === formData.state);
      const selectedCity = cities.find(c => c.id === formData.city);

      const eventData = {
        title: formData.title,
        description: formData.description,
        date: formData.date.toISOString(),
        location: formData.location,
        state: selectedState?.name || "",
        city: selectedCity?.name || "",
        category: formData.category,
        price: formData.price ? parseFloat(formData.price) : null,
        image: formData.image || null,
        lat: coordinates?.lat || null,
        lng: coordinates?.lng || null,
        cityId: formData.city || null
      };

      const response = await fetch('/api/events', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(eventData)
      });

      if (response.ok) {
        const event = await response.json();
        router.push(`/events/${event.id}`);
      } else {
        const error = await response.json();
        alert(error.error || 'Failed to create event');
      }
    } catch (error) {
      console.error('Error creating event:', error);
      alert('Failed to create event');
    } finally {
      setLoading(false);
    }
  };

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <>
      <style jsx global>{`
        /* Custom DatePicker Styling */
        .react-datepicker-wrapper {
          width: 100%;
          position: relative;
          z-index: 100;
        }
        .react-datepicker {
          font-family: inherit;
          border: 1px solid #e5e7eb;
          border-radius: 12px;
          box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
          z-index: 9999 !important;
        }
        .react-datepicker-popper {
          z-index: 9999 !important;
        }
        .react-datepicker__input-container input {
          color: #374151 !important;
          font-weight: 500;
        }
        
        .react-datepicker__header {
          background-color: #f8fafc;
          border-bottom: 1px solid #e5e7eb;
          border-radius: 12px 12px 0 0;
          padding: 16px 0;
        }
        
        .react-datepicker__current-month,
        .react-datepicker-time__header {
          color: #1f2937;
          font-weight: 600;
          font-size: 16px;
        }
        
        .react-datepicker__day-name,
        .react-datepicker__day,
        .react-datepicker__time-name {
          color: #374151;
          font-weight: 500;
        }
        
        .react-datepicker__day:hover {
          background-color: #ddd6fe;
          color: #5b21b6;
        }
        
        .react-datepicker__day--selected,
        .react-datepicker__day--keyboard-selected {
          background-color: #8b5cf6;
          color: white;
          font-weight: 600;
        }
        
        .react-datepicker__day--today {
          background-color: #fef3c7;
          color: #92400e;
          font-weight: 600;
        }
        
        .react-datepicker__time-list-item {
          color: #374151;
          font-weight: 500;
        }
        
        .react-datepicker__time-list-item:hover {
          background-color: #ddd6fe;
          color: #5b21b6;
        }
        
        .react-datepicker__time-list-item--selected {
          background-color: #8b5cf6;
          color: white;
          font-weight: 600;
        }
        
        .react-datepicker__navigation {
          top: 18px;
        }
        
        .react-datepicker__navigation--previous {
          border-right-color: #6b7280;
        }
        
        .react-datepicker__navigation--next {
          border-left-color: #6b7280;
        }
        
        .react-datepicker__navigation:hover *::before {
          border-color: #8b5cf6;
        }
      `}</style>
      
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-600 via-blue-600 to-teal-500 py-16">
        <div className="container mx-auto px-4">
          <div className="flex items-center gap-4 mb-6">
            <Link href="/events">
              <motion.button 
                whileHover={{ scale: 1.05 }}
                className="p-3 bg-white/20 backdrop-blur-lg text-white rounded-full hover:bg-white/30 transition-all duration-300"
              >
                <ArrowLeft className="h-6 w-6" />
              </motion.button>
            </Link>
            <h1 className="text-4xl md:text-5xl font-bold text-white">Create New Event</h1>
          </div>
          <p className="text-xl text-blue-100 max-w-2xl">
            Share your amazing event with the community and bring people together
          </p>
        </div>
      </div>

      {/* Form */}
      <div className="container mx-auto px-4 py-12">
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="max-w-4xl mx-auto"
        >
          <form onSubmit={handleSubmit} className="space-y-8">
            
            {/* Basic Information */}
            <div className="bg-white/70 backdrop-blur-lg rounded-2xl p-8 border border-white/30 shadow-lg">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Calendar className="h-6 w-6 text-purple-600" />
                </div>
                Basic Information
              </h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white/80 text-gray-900"
                    placeholder="Enter your event title"
                    required
                  />
                </div>
                
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description *
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={4}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white/80 text-gray-900"
                    placeholder="Describe your event..."
                    required
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Category *
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white/80 text-gray-900"
                    required
                  >
                    <option value="">Select category</option>
                    {categories.map(category => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Price (₹)
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 font-bold text-lg">₹</span>
                    <input
                      type="number"
                      value={formData.price}
                      onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                      className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white/80 text-gray-900"
                      placeholder="Leave empty for free event"
                      min="0"
                      step="1"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Date & Time */}
            <div className="bg-white/70 backdrop-blur-lg rounded-2xl p-8 border border-white/30 shadow-lg relative z-50">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Clock className="h-6 w-6 text-blue-600" />
                </div>
                Date & Time
              </h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div className="relative z-50">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Date *
                  </label>
                  <div className="relative z-50">
                    <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 z-10" />
                    <DatePicker
                      selected={formData.date}
                      onChange={(date) => setFormData(prev => ({ ...prev, date: date || new Date() }))}
                      dateFormat="MMMM d, yyyy"
                      className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white text-gray-900 font-medium"
                      minDate={new Date()}
                      placeholderText="Select event date"
                      popperClassName="z-[9999]"
                      required
                    />
                  </div>
                </div>
                
                <div className="relative z-50">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Event Time *
                  </label>
                  <div className="relative z-50">
                    <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5 z-10" />
                    <DatePicker
                      selected={formData.date}
                      onChange={(date) => setFormData(prev => ({ ...prev, date: date || new Date() }))}
                      showTimeSelect
                      showTimeSelectOnly
                      timeIntervals={15}
                      timeCaption="Time"
                      dateFormat="h:mm aa"
                      className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white text-gray-900 font-medium"
                      placeholderText="Select event time"
                      popperClassName="z-[9999]"
                      required
                    />
                  </div>
                </div>
              </div>
              
              <div className="mt-4 p-4 bg-blue-50 rounded-xl border border-blue-200">
                <div className="flex items-start gap-3">
                  <div className="p-1 bg-blue-100 rounded-full mt-0.5">
                    <Clock className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-blue-900">Selected Date & Time</p>
                    <p className="text-lg font-bold text-blue-800">
                      {formData.date.toLocaleDateString('en-US', { 
                        weekday: 'long',
                        year: 'numeric', 
                        month: 'long', 
                        day: 'numeric'
                      })} at {formData.date.toLocaleTimeString('en-US', { 
                        hour: 'numeric', 
                        minute: '2-digit',
                        hour12: true 
                      })}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Location */}
            <div className="bg-white/70 backdrop-blur-lg rounded-2xl p-8 border border-white/30 shadow-lg relative z-10">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                <div className="p-2 bg-green-100 rounded-lg">
                  <MapPin className="h-6 w-6 text-green-600" />
                </div>
                Location
              </h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    State *
                  </label>
                  <select
                    value={formData.state}
                    onChange={(e) => handleStateChange(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white/80 text-gray-900"
                    required
                  >
                    <option value="">Select state</option>
                    {states.map(state => (
                      <option key={state.id} value={state.id}>{state.name}</option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    City *
                  </label>
                  <select
                    value={formData.city}
                    onChange={(e) => handleCityChange(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white/80 text-gray-900"
                    disabled={!formData.state}
                    required
                  >
                    <option value="">Select city</option>
                    {filteredCities.map(city => (
                      <option key={city.id} value={city.id}>{city.name}</option>
                    ))}
                  </select>
                </div>
                
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Venue Address *
                  </label>
                  <input
                    type="text"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white/80 text-gray-900"
                    placeholder="Enter venue address"
                    required
                  />
                </div>
                
                <div className="md:col-span-2">
                  <button
                    type="button"
                    onClick={() => setShowLocationPicker(true)}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-teal-500 text-white font-medium rounded-xl hover:shadow-lg transition-all duration-300"
                  >
                    📍 Pick Location on Map
                  </button>
                  {coordinates && (
                    <div className="mt-2 text-sm text-gray-600">
                      Coordinates: {coordinates.lat.toFixed(4)}, {coordinates.lng.toFixed(4)}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Event Image */}
            <div className="bg-white/70 backdrop-blur-lg rounded-2xl p-8 border border-white/30 shadow-lg">
              <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                <div className="p-2 bg-orange-100 rounded-lg">
                  <ImageIcon className="h-6 w-6 text-orange-600" />
                </div>
                Event Image
              </h2>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Image URL
                </label>
                <input
                  type="url"
                  value={formData.image}
                  onChange={(e) => setFormData(prev => ({ ...prev, image: e.target.value }))}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white/80 text-gray-900"
                  placeholder="https://example.com/event-image.jpg"
                />
                <p className="mt-2 text-sm text-gray-500">
                  Optional: Add an image URL to make your event more attractive
                </p>
              </div>
            </div>

            {/* Submit Button */}
            <div className="flex justify-end gap-4">
              <Link href="/events">
                <button
                  type="button"
                  className="px-8 py-4 border-2 border-gray-300 text-gray-700 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-300"
                >
                  Cancel
                </button>
              </Link>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                type="submit"
                disabled={loading}
                className="px-8 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Creating Event...
                  </div>
                ) : (
                  'Create Event'
                )}
              </motion.button>
            </div>
          </form>
        </motion.div>
      </div>

      {/* Location Picker Modal */}
      {showLocationPicker && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl w-full max-w-4xl h-[600px] flex flex-col">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">Pick Event Location</h3>
              <p className="text-gray-600">Click on the map to set the exact location of your event</p>
            </div>
            <div className="flex-1">
              <LocationPickerMap
                initialLat={coordinates?.lat || 20.5937}
                initialLng={coordinates?.lng || 78.9629}
                onLocationSelect={handleLocationSelect}
              />
            </div>
            <div className="p-6 border-t border-gray-200 flex justify-end gap-4">
              <button
                onClick={() => setShowLocationPicker(false)}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-300"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
    </>
  );
}
