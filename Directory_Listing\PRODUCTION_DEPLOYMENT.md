# Production Deployment Guide

This guide provides step-by-step instructions for deploying the City Directory application to production.

## 🚀 Quick Production Checklist

### 1. Environment Variables Configuration

Copy `.env.example` to `.env.production` and configure:

```bash
# Required Production Variables
NEXTAUTH_URL="https://yourdomain.com"
NEXTAUTH_SECRET="$(openssl rand -base64 32)"
DATABASE_URL="your-production-database-url"
GOOGLE_CLIENT_ID="your-google-oauth-client-id"
GOOGLE_CLIENT_SECRET="your-google-oauth-client-secret"
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY="your-google-maps-api-key"
```

### 2. Database Setup

#### Option A: PostgreSQL (Recommended for Production)
```bash
# Update DATABASE_URL in .env.production
DATABASE_URL="postgresql://username:password@localhost:5432/citydir_prod"

# Run migrations
npx prisma migrate deploy
npx prisma generate
```

#### Option B: MySQL
```bash
DATABASE_URL="mysql://username:password@localhost:3306/citydir_prod"
```

#### Option C: SQLite (Development/Small Scale)
```bash
DATABASE_URL="file:./prisma/prod.db"
```

### 3. Build and Deploy

```bash
# Install dependencies
npm ci

# Build the application
npm run build

# Start production server
npm start
```

## 🌐 Platform-Specific Deployment

### Vercel (Recommended)

1. **Connect Repository**
   - Import project from GitHub/GitLab
   - Vercel auto-detects Next.js configuration

2. **Configure Environment Variables**
   - Add all variables from `.env.example`
   - Set `NEXTAUTH_URL` to your Vercel domain

3. **Deploy**
   - Automatic deployment on git push
   - Preview deployments for PRs

### Railway

```bash
# Install Railway CLI
npm install -g @railway/cli

# Login and deploy
railway login
railway init
railway up
```

### Docker Deployment

```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

### DigitalOcean/AWS/GCP

1. **Set up server** (Ubuntu 20.04+ recommended)
2. **Install Node.js 18+**
3. **Clone repository**
4. **Configure environment variables**
5. **Set up process manager** (PM2 recommended)

```bash
# Install PM2
npm install -g pm2

# Start application
pm2 start npm --name "city-directory" -- start
pm2 startup
pm2 save
```

## 🔧 Production Optimizations

### 1. Database Optimizations

```sql
-- Add indexes for better performance
CREATE INDEX idx_listings_status ON Listing(status);
CREATE INDEX idx_listings_city_state ON Listing(cityId, stateId);
CREATE INDEX idx_listings_category ON Listing(categoryId);
```

### 2. Image Optimization

- Configure Cloudinary or similar service
- Update `next.config.ts` with your CDN domains
- Optimize image uploads in admin panel

### 3. Caching Strategy

```typescript
// Add to API routes for caching
export const revalidate = 3600; // 1 hour cache

// Or use ISR for pages
export const revalidate = 300; // 5 minutes
```

### 4. Security Headers

```typescript
// next.config.ts additions
const nextConfig = {
  headers: async () => [
    {
      source: '/(.*)',
      headers: [
        {
          key: 'X-Frame-Options',
          value: 'DENY',
        },
        {
          key: 'X-Content-Type-Options',
          value: 'nosniff',
        },
        {
          key: 'Referrer-Policy',
          value: 'strict-origin-when-cross-origin',
        },
      ],
    },
  ],
};
```

## 📊 Monitoring and Analytics

### 1. Error Tracking (Sentry)

```bash
npm install @sentry/nextjs
```

### 2. Analytics (Vercel Analytics)

```bash
npm install @vercel/analytics
```

### 3. Performance Monitoring

- Set up Vercel Speed Insights
- Configure Web Vitals tracking
- Monitor database performance

## 🔐 Security Considerations

### 1. Authentication Security

- Use strong `NEXTAUTH_SECRET` (32+ characters)
- Configure OAuth providers with production domains
- Set up proper CORS policies

### 2. Database Security

- Use connection pooling
- Regular backups
- Restrict database access

### 3. API Security

- Rate limiting implementation
- Input validation
- CSRF protection

## 🚀 Scaling Considerations

### 1. Horizontal Scaling

- Use load balancers
- Implement session storage (Redis)
- CDN for static assets

### 2. Database Scaling

- Read replicas
- Connection pooling
- Query optimization

### 3. Caching

- Redis for session storage
- CDN for images and static assets
- API response caching

## 📋 Post-Deployment Tasks

1. **Test all functionality**
   - User registration/login
   - Listing creation/editing
   - Location detection
   - Admin panel access

2. **Set up monitoring**
   - Error tracking
   - Performance monitoring
   - Uptime monitoring

3. **Configure backups**
   - Database backups
   - File storage backups
   - Environment configuration backups

4. **DNS and SSL**
   - Configure domain DNS
   - Set up SSL certificate
   - Configure redirects

## 🔍 Troubleshooting

### Common Issues

1. **Authentication not working**
   - Check `NEXTAUTH_URL` matches domain
   - Verify OAuth provider configuration
   - Check `NEXTAUTH_SECRET` is set

2. **Maps not loading**
   - Verify `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY`
   - Check API quotas and billing
   - Verify domain restrictions

3. **Database connection issues**
   - Check `DATABASE_URL` format
   - Verify database server access
   - Run migrations: `npx prisma migrate deploy`

4. **Build failures**
   - Check Node.js version (18+)
   - Clear cache: `rm -rf .next node_modules`
   - Reinstall: `npm ci`

## 📞 Support

For deployment assistance:
- Check logs for specific error messages
- Verify all environment variables are set
- Ensure database migrations are applied
- Test API endpoints individually

---

**Note**: This application is production-ready with proper environment variable configuration and follows Next.js best practices for deployment.
