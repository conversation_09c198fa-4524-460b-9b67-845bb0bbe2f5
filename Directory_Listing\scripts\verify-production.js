#!/usr/bin/env node

/**
 * Production Readiness Verification Script
 * Run with: node scripts/verify-production.js
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Production Readiness...\n');

const checks = [];

// Check 1: Environment variables
function checkEnvironmentVariables() {
  const requiredEnvVars = [
    'NEXTAUTH_SECRET',
    'DATABASE_URL',
  ];
  
  const productionEnvVars = [
    'NEXTAUTH_URL',
    'GOOGLE_CLIENT_ID', 
    'GOOGLE_CLIENT_SECRET',
    'NEXT_PUBLIC_GOOGLE_MAPS_API_KEY',
  ];

  const missing = requiredEnvVars.filter(env => !process.env[env]);
  const missingProduction = productionEnvVars.filter(env => !process.env[env]);

  if (missing.length === 0) {
    checks.push({ name: 'Required Environment Variables', status: '✅ PASS' });
  } else {
    checks.push({ name: 'Required Environment Variables', status: `❌ FAIL - Missing: ${missing.join(', ')}` });
  }

  if (missingProduction.length === 0) {
    checks.push({ name: 'Production Environment Variables', status: '✅ PASS' });
  } else {
    checks.push({ name: 'Production Environment Variables', status: `⚠️  WARN - Missing: ${missingProduction.join(', ')}` });
  }
}

// Check 2: NextAuth Secret strength
function checkNextAuthSecret() {
  const secret = process.env.NEXTAUTH_SECRET;
  if (!secret) {
    checks.push({ name: 'NextAuth Secret', status: '❌ FAIL - Not configured' });
  } else if (secret.length < 32) {
    checks.push({ name: 'NextAuth Secret', status: '❌ FAIL - Too short (minimum 32 characters)' });
  } else {
    checks.push({ name: 'NextAuth Secret', status: '✅ PASS' });
  }
}

// Check 3: Production URL configuration
function checkProductionUrl() {
  const url = process.env.NEXTAUTH_URL;
  if (!url) {
    checks.push({ name: 'Production URL', status: '⚠️  WARN - NEXTAUTH_URL not set' });
  } else if (url.includes('localhost') || url.includes('127.0.0.1')) {
    checks.push({ name: 'Production URL', status: '❌ FAIL - Using localhost in production' });
  } else if (url.startsWith('https://')) {
    checks.push({ name: 'Production URL', status: '✅ PASS' });
  } else {
    checks.push({ name: 'Production URL', status: '⚠️  WARN - Should use HTTPS in production' });
  }
}

// Check 4: Database configuration
function checkDatabaseConfig() {
  const dbUrl = process.env.DATABASE_URL;
  if (!dbUrl) {
    checks.push({ name: 'Database Configuration', status: '❌ FAIL - DATABASE_URL not configured' });
    return;
  }

  if (dbUrl.includes('dev.db') && process.env.NODE_ENV === 'production') {
    checks.push({ name: 'Database Configuration', status: '⚠️  WARN - Using development database in production' });
  } else {
    checks.push({ name: 'Database Configuration', status: '✅ PASS' });
  }
}

// Check 5: Security headers configuration
function checkSecurityConfig() {
  const configPath = path.join(process.cwd(), 'next.config.ts');
  if (fs.existsSync(configPath)) {
    const config = fs.readFileSync(configPath, 'utf8');
    if (config.includes('X-Frame-Options') && config.includes('headers()')) {
      checks.push({ name: 'Security Headers', status: '✅ PASS' });
    } else {
      checks.push({ name: 'Security Headers', status: '⚠️  WARN - Security headers not configured' });
    }
  } else {
    checks.push({ name: 'Security Headers', status: '❌ FAIL - next.config.ts not found' });
  }
}

// Check 6: Build output
function checkBuildOutput() {
  const buildPath = path.join(process.cwd(), '.next');
  if (fs.existsSync(buildPath)) {
    checks.push({ name: 'Build Output', status: '✅ PASS' });
  } else {
    checks.push({ name: 'Build Output', status: '❌ FAIL - Run "npm run build" first' });
  }
}

// Check 7: Package.json production scripts
function checkProductionScripts() {
  const packagePath = path.join(process.cwd(), 'package.json');
  if (fs.existsSync(packagePath)) {
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    const hasProductionScripts = packageJson.scripts && 
      packageJson.scripts['build:production'] && 
      packageJson.scripts['start:production'];
    
    if (hasProductionScripts) {
      checks.push({ name: 'Production Scripts', status: '✅ PASS' });
    } else {
      checks.push({ name: 'Production Scripts', status: '⚠️  WARN - Production scripts missing' });
    }
  }
}

// Run all checks
async function runChecks() {
  checkEnvironmentVariables();
  checkNextAuthSecret();
  checkProductionUrl();
  checkDatabaseConfig();
  checkSecurityConfig();
  checkBuildOutput();
  checkProductionScripts();

  // Display results
  console.log('📋 Production Readiness Report:\n');
  checks.forEach(check => {
    console.log(`${check.status.padEnd(15)} ${check.name}`);
  });

  const failed = checks.filter(c => c.status.includes('❌')).length;
  const warnings = checks.filter(c => c.status.includes('⚠️')).length;
  const passed = checks.filter(c => c.status.includes('✅')).length;

  console.log('\n📊 Summary:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`⚠️  Warnings: ${warnings}`);
  console.log(`❌ Failed: ${failed}`);

  if (failed === 0) {
    console.log('\n🎉 Production ready! All critical checks passed.');
    if (warnings > 0) {
      console.log('📝 Consider addressing warnings for optimal production setup.');
    }
  } else {
    console.log('\n🚨 Not production ready. Please fix failed checks before deploying.');
    process.exit(1);
  }
}

// Production deployment checklist
function showDeploymentChecklist() {
  console.log('\n📝 Pre-Deployment Checklist:');
  console.log('□ Set all required environment variables');
  console.log('□ Generate secure NEXTAUTH_SECRET: openssl rand -base64 32');
  console.log('□ Configure production database');
  console.log('□ Set up SSL certificate');
  console.log('□ Configure domain DNS');
  console.log('□ Test Google OAuth with production domain');
  console.log('□ Verify Google Maps API key and quotas');
  console.log('□ Set up monitoring and logging');
  console.log('□ Configure backup strategy');
  console.log('□ Test health check endpoint: /api/health');
  console.log('□ Perform load testing');
}

// Run the verification
runChecks().then(() => {
  showDeploymentChecklist();
}).catch(console.error);
