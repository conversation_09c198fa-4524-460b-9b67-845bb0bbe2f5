"use client";
import { useState, useEffect, Suspense } from "react";
import Link from "next/link";
import { useSearchParams } from "next/navigation";
import dynamic from "next/dynamic";

// Dynamically import MapView to avoid SSR issues
const MapView = dynamic(() => import("@/components/MapView"), {
  ssr: false,
  loading: () => (
    <div className="w-full h-screen bg-gray-100 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading map...</p>
      </div>
    </div>
  ),
});

interface Listing {
  id: string;
  title: string;
  description: string;
  address: string;
  city: string;
  state: string;
  locationLat?: number;
  locationLng?: number;
  category: {
    id: string;
    name: string;
  };
  tags: Array<{
    id: string;
    name: string;
  }>;
  images: Array<{
    id: string;
    url: string;
  }>;
  user: {
    id: string;
    name?: string;
  };
  createdAt: string;
}

interface Category {
  id: string;
  name: string;
}

export default function MapPage() {
  return (
    <Suspense fallback={<div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-pulse">
          <div className="w-8 h-8 bg-blue-500 rounded-full animate-bounce mx-auto mb-4"></div>
          <p className="text-slate-600">Loading map...</p>
        </div>
      </div>
    </div>}>
      <MapPageContent />
    </Suspense>
  );
}

function MapPageContent() {
  const [listings, setListings] = useState<Listing[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedListing, setSelectedListing] = useState<Listing | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [showMobileDetails, setShowMobileDetails] = useState(false);

  const searchParams = useSearchParams();
  
  useEffect(() => {
    // Get URL parameters
    const search = searchParams.get("search") || "";
    const category = searchParams.get("category") || "";
    
    setSearchTerm(search);
    setSelectedCategory(category);
    
    fetchData();
  }, [searchParams]);

  async function fetchData() {
    try {
      setLoading(true);
      
      // Build query parameters
      const queryParams = new URLSearchParams();
      if (searchTerm) queryParams.append('search', searchTerm);
      if (selectedCategory) queryParams.append('category', selectedCategory);
      
      // Fetch filtered listings
      const listingsRes = await fetch(`/api/listings?${queryParams}`);
      const categoriesRes = await fetch("/api/admin/categories");

      if (listingsRes.ok) {
        const listingsData = await listingsRes.json();
        setListings(Array.isArray(listingsData) ? listingsData : []);
      }

      if (categoriesRes.ok) {
        const categoriesData = await categoriesRes.json();
        setCategories(Array.isArray(categoriesData) ? categoriesData : []);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      setListings([]);
      setCategories([]);
    } finally {
      setLoading(false);
    }
  }

  // Filter listings with valid coordinates for map display
  const mappableListings = listings.filter(listing => 
    listing.locationLat && listing.locationLng
  );

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    fetchData();
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-slate-600">Loading map and listings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-white">
      {/* Header Controls */}
      <div className="bg-white border-b border-gray-200 p-4 flex-shrink-0">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Title and Back Link */}
            <div className="flex items-center gap-4">
              <Link
                href="/listings"
                className="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to List
              </Link>
              <h1 className="text-xl font-bold text-gray-900">
                Map View - {mappableListings.length} Locations
              </h1>
            </div>

            {/* Search and Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-3">
              <form onSubmit={handleSearch} className="flex gap-2">
                <input
                  type="text"
                  placeholder="Search listings..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                />
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Search
                </button>
              </form>
              
              <select
                value={selectedCategory}
                onChange={(e) => {
                  setSelectedCategory(e.target.value);
                  setTimeout(fetchData, 100);
                }}
                className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-gray-900 bg-white"
                title="Filter by category"
              >
                <option value="">All Categories</option>
                {categories.map(category => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Map Container */}
      <div className="flex-1 relative">
        {mappableListings.length === 0 ? (
          <div className="h-full flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <div className="text-6xl mb-4">📍</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No mappable listings found</h3>
              <p className="text-gray-600 mb-6">
                {listings.length > 0 
                  ? "The listings matching your criteria don't have location coordinates yet."
                  : "No listings match your search criteria."
                }
              </p>
              <Link
                href="/listings"
                className="inline-block px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                View All Listings
              </Link>
            </div>
          </div>
        ) : (
          <MapView
            listings={mappableListings}
            onListingSelect={(listing) => {
              setSelectedListing(listing);
              setShowMobileDetails(true);
            }}
            className="w-full h-full"
          />
        )}

        {/* Mobile Bottom Sheet */}
        {selectedListing && (
          <div className={`absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 transform transition-transform duration-300 lg:hidden ${
            showMobileDetails ? 'translate-y-0' : 'translate-y-full'
          }`}>
            <div className="p-4">
              {/* Handle */}
              <div className="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4"></div>
              
              {/* Close Button */}
              <button
                onClick={() => setShowMobileDetails(false)}
                className="absolute top-4 right-4 p-2 text-gray-400 hover:text-gray-600"
                aria-label="Close details"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </button>

              {/* Listing Details */}
              <div className="flex gap-4">
                <img
                  src={selectedListing.images[0]?.url || "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&w=100&q=80"}
                  alt={selectedListing.title}
                  className="w-20 h-20 object-cover rounded-lg flex-shrink-0"
                />
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900">{selectedListing.title}</h3>
                  <p className="text-sm text-gray-600">{selectedListing.category.name}</p>
                  <p className="text-sm text-gray-500 mt-1">{selectedListing.city}, {selectedListing.state}</p>
                  <p className="text-sm text-gray-700 mt-2 line-clamp-2">{selectedListing.description}</p>
                  <Link
                    href={`/listings/${selectedListing.id}`}
                    className="inline-block mt-3 px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    View Full Details
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Desktop Sidebar */}
        {selectedListing && (
          <div className="hidden lg:block absolute top-4 left-4 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4">
            <button
              onClick={() => setSelectedListing(null)}
              className="absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-600"
              aria-label="Close details"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>

            <div className="space-y-3">
              <img
                src={selectedListing.images[0]?.url || "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&w=300&q=80"}
                alt={selectedListing.title}
                className="w-full h-40 object-cover rounded-lg"
              />
              <div>
                <h3 className="font-semibold text-gray-900 text-lg">{selectedListing.title}</h3>
                <p className="text-sm text-blue-600">{selectedListing.category.name}</p>
                <p className="text-sm text-gray-500 mt-1">{selectedListing.address}</p>
                <p className="text-sm text-gray-500">{selectedListing.city}, {selectedListing.state}</p>
              </div>
              <p className="text-sm text-gray-700">{selectedListing.description}</p>
              {selectedListing.tags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {selectedListing.tags.map(tag => (
                    <span
                      key={tag.id}
                      className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
                    >
                      {tag.name}
                    </span>
                  ))}
                </div>
              )}
              <Link
                href={`/listings/${selectedListing.id}`}
                className="block w-full text-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                View Full Details
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
