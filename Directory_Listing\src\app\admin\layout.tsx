"use client";
import Link from "next/link";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/signup");
    } else if (status === "authenticated" && session?.user?.role !== "ADMIN") {
      router.push("/");
    }
  }, [status, session, router]);

  if (status === "loading") {
    return <div>Loading...</div>;
  }

  if (!session || session.user?.role !== "ADMIN") {
    return null;
  }

  const handleLogout = async () => {
    await signOut({ 
      redirect: false,
      callbackUrl: "/" 
    });
    router.push("/");
  };

  return (
    <div className="flex min-h-screen">
      <aside className="w-64 bg-gray-900 text-white flex flex-col p-4 space-y-4">
        <h2 className="text-xl font-bold mb-6">Admin Panel</h2>
        <nav className="flex flex-col space-y-2">
          <Link href="/admin" className="px-3 py-2 rounded hover:bg-gray-700 transition-colors">
            Dashboard
          </Link>
          <Link href="/admin/listings" className="px-3 py-2 rounded hover:bg-gray-700 transition-colors">
            Manage Listings
          </Link>
          <Link href="/admin/users" className="px-3 py-2 rounded hover:bg-gray-700 transition-colors">
            Manage Users
          </Link>
          <Link href="/admin/categories" className="px-3 py-2 rounded hover:bg-gray-700 transition-colors">
            Categories
          </Link>
          <Link href="/admin/tags" className="px-3 py-2 rounded hover:bg-gray-700 transition-colors">
            Tags
          </Link>
          <Link href="/admin/locations" className="px-3 py-2 rounded hover:bg-gray-700 transition-colors">
            Locations
          </Link>
          <Link href="/admin/events" className="px-3 py-2 rounded hover:bg-gray-700 transition-colors">
            Events
          </Link>
          <Link href="/admin/pages" className="px-3 py-2 rounded hover:bg-gray-700 transition-colors">
            CMS Pages
          </Link>
          <Link href="/admin/blog" className="px-3 py-2 rounded hover:bg-gray-700 transition-colors">
            Blog Posts
          </Link>
          <Link href="/admin/media" className="px-3 py-2 rounded hover:bg-gray-700 transition-colors">
            Media Library
          </Link>
          <hr className="border-gray-700 my-4" />
          <Link href="/dashboard" className="px-3 py-2 rounded hover:bg-gray-700 transition-colors">
            View Site
          </Link>
          <button 
            onClick={handleLogout}
            className="text-left px-3 py-2 rounded hover:bg-gray-700 transition-colors"
          >
            Sign Out
          </button>
        </nav>
      </aside>
      <main className="flex-1 bg-gray-50 p-8">{children}</main>
    </div>
  );
} 