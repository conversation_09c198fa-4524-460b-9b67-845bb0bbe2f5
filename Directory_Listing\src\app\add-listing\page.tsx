"use client";
import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { INDIAN_STATES } from "@/lib/indian-states";
import { GeocodingService, type GeocodingResult } from "@/services/GeocodingService";
import { LocationService } from "@/services/LocationService";
import dynamic from 'next/dynamic';

// Dynamically import the LocationPickerMap to avoid SSR issues
const LocationPickerMap = dynamic(() => import('@/components/LocationPickerMap'), {
  ssr: false,
  loading: () => <div className="w-full h-64 bg-slate-100 rounded-lg flex items-center justify-center">Loading map...</div>
});

interface Category {
  id: string;
  name: string;
}

interface Tag {
  id: string;
  name: string;
}

interface FormData {
  title: string;
  description: string;
  address: string;
  city: string;
  state: string;
  country: string;
  pincode: string;
  phone: string;
  email: string;
  website: string;
  categoryId: string;
  tagIds: string[];
  locationLat?: number;
  locationLng?: number;
}

export default function AddListingPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [step, setStep] = useState(1);
  const [geocoding, setGeocoding] = useState(false);
  const [showMapPicker, setShowMapPicker] = useState(false);
  
  const [formData, setFormData] = useState<FormData>({
    title: "",
    description: "",
    address: "",
    city: "",
    state: "",
    country: "India",
    pincode: "",
    phone: "",
    email: "",
    website: "",
    categoryId: "",
    tagIds: [],
    locationLat: undefined,
    locationLng: undefined,
  });

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/api/auth/signin");
      return;
    }
    
    fetchData();
    
    // Pre-fill location from stored selection
    const storedLocation = LocationService.getStoredLocation();
    if (storedLocation) {
      setFormData(prev => ({
        ...prev,
        city: storedLocation.city,
        state: storedLocation.state,
        country: "India"
      }));
    }
  }, [status, router]);

  async function fetchData() {
    try {
      const [categoriesRes, tagsRes] = await Promise.all([
        fetch("/api/admin/categories"),
        fetch("/api/admin/tags")
      ]);

      console.log("Categories response status:", categoriesRes.status);
      console.log("Tags response status:", tagsRes.status);

      if (!categoriesRes.ok || !tagsRes.ok) {
        throw new Error(`Failed to fetch data: Categories ${categoriesRes.status}, Tags ${tagsRes.status}`);
      }

      const [categoriesData, tagsData] = await Promise.all([
        categoriesRes.json(),
        tagsRes.json()
      ]);

      console.log("Categories data:", categoriesData);
      console.log("Tags data:", tagsData);

      setCategories(categoriesData);
      setTags(tagsData);
    } catch (error) {
      console.error("Error fetching data:", error);
      setError("Failed to load form data. Please refresh the page and try again.");
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleNumberInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const numValue = value === '' ? undefined : parseFloat(value);
    setFormData(prev => ({ ...prev, [name]: numValue }));
  };

  const handleGeocodeAddress = async () => {
    // Don't pre-format the address, let GeocodingService.formatAddressForGeocoding handle it
    if (!formData.address?.trim() || !formData.city?.trim()) {
      setError("Please fill in the address and city fields before geocoding.");
      return;
    }

    setGeocoding(true);
    setError(null);

    try {
      // Get multiple fallback addresses to try
      const fallbackAddresses = GeocodingService.createFallbackAddresses(
        formData.address,
        formData.city,
        formData.state,
        formData.country
      );
      
      console.log('🏠 Trying addresses:', fallbackAddresses);
      
      let result: GeocodingResult | null = null;
      let successfulAddress = '';
      
      // Try each address until one works
      for (const addressToTry of fallbackAddresses) {
        console.log(`� Trying: ${addressToTry}`);
        result = await GeocodingService.geocodeAddress(addressToTry);
        if (result) {
          successfulAddress = addressToTry;
          break;
        }
      }
      
      if (result) {
        setFormData(prev => ({
          ...prev,
          locationLat: result.lat,
          locationLng: result.lng
        }));
        
        console.log('📍 Geocoding successful:', { lat: result.lat, lng: result.lng });
        
        if (successfulAddress === fallbackAddresses[0]) {
          setError(`✅ Coordinates found: ${result.lat.toFixed(6)}, ${result.lng.toFixed(6)}`);
        } else {
          setError(`✅ Coordinates found for "${successfulAddress}": ${result.lat.toFixed(6)}, ${result.lng.toFixed(6)}. You may need to adjust manually for the exact address.`);
        }
      } else {
        setError("❌ Unable to find coordinates for this address. Please enter them manually or try a simpler address.");
      }
    } catch (error) {
      console.error('Geocoding error:', error);
      setError("❌ Failed to geocode address. Please check your internet connection or enter coordinates manually.");
    } finally {
      setGeocoding(false);
    }
  };

  const handleMapLocationSelect = (lat: number, lng: number) => {
    setFormData(prev => ({
      ...prev,
      locationLat: lat,
      locationLng: lng
    }));
    
    setError(`✅ Location selected from map: ${lat.toFixed(6)}, ${lng.toFixed(6)}`);
    console.log('🗺️ Map location selected:', { lat, lng });
  };

  const handleTagToggle = (tagId: string) => {
    setFormData(prev => ({
      ...prev,
      tagIds: prev.tagIds.includes(tagId)
        ? prev.tagIds.filter(id => id !== tagId)
        : [...prev.tagIds, tagId]
    }));
  };

  const validateStep1 = () => {
    return formData.title.trim() && formData.description.trim() && formData.categoryId;
  };

  const validateStep2 = () => {
    return formData.address.trim() && formData.city.trim() && formData.state.trim();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    console.log("Session:", session);
    console.log("User ID:", session?.user?.id);
    
    if (!session?.user?.id) {
      setError("You must be logged in to create a listing. Please sign in and try again.");
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const submissionData = {
        ...formData,
        userId: session.user.id,
        tagIds: formData.tagIds
      };

      console.log("Submitting listing data:", submissionData);

      const response = await fetch("/api/listings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(submissionData),
      });

      console.log("Response status:", response.status);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create listing");
      }

      const result = await response.json();
      console.log("Created listing:", result);

      setSuccess(true);
      
      // Redirect to success page or listing details after 2 seconds
      setTimeout(() => {
        router.push("/dashboard");
      }, 2000);

    } catch (err) {
      console.error("Error creating listing:", err);
      setError(err instanceof Error ? err.message : "Failed to create listing. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-xl p-8 max-w-md w-full mx-4 text-center">
          <div className="text-6xl mb-4">🎉</div>
          <h2 className="text-2xl font-bold text-slate-900 mb-4">Listing Created Successfully!</h2>
          <p className="text-slate-600 mb-6">
            Your business listing has been submitted and will be reviewed shortly.
          </p>
          <div className="text-sm text-slate-500">
            Redirecting to your dashboard...
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto px-6 lg:px-8 py-12">
        
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">
            Add Your Business
          </h1>
          <p className="text-xl text-slate-600 max-w-2xl mx-auto">
            Get discovered by thousands of potential customers in your area
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="max-w-3xl mx-auto mb-8">
          <div className="flex items-center justify-center space-x-4">
            <div className={`flex items-center ${step >= 1 ? 'text-blue-600' : 'text-slate-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-blue-600 text-white' : 'bg-slate-200'}`}>
                1
              </div>
              <span className="ml-2 font-medium">Basic Info</span>
            </div>
            <div className={`w-16 h-0.5 ${step >= 2 ? 'bg-blue-600' : 'bg-slate-200'}`}></div>
            <div className={`flex items-center ${step >= 2 ? 'text-blue-600' : 'text-slate-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-blue-600 text-white' : 'bg-slate-200'}`}>
                2
              </div>
              <span className="ml-2 font-medium">Location</span>
            </div>
            <div className={`w-16 h-0.5 ${step >= 3 ? 'bg-blue-600' : 'bg-slate-200'}`}></div>
            <div className={`flex items-center ${step >= 3 ? 'text-blue-600' : 'text-slate-400'}`}>
              <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-blue-600 text-white' : 'bg-slate-200'}`}>
                3
              </div>
              <span className="ml-2 font-medium">Contact & Tags</span>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="max-w-3xl mx-auto">
          <form onSubmit={handleSubmit} className="bg-white rounded-2xl shadow-xl p-8">
            
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600">{error}</p>
              </div>
            )}

            {/* Step 1: Basic Information */}
            {step === 1 && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-slate-900 mb-6">Basic Information</h3>
                
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Business Name *
                  </label>
                  <input
                    type="text"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                    placeholder="Enter your business name"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Category *
                  </label>
                  <select
                    name="categoryId"
                    value={formData.categoryId}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                    required
                    title="Select a category for your listing"
                  >
                    <option value="">Select a category</option>
                    {categories.map(category => (
                      <option key={category.id} value={category.id}>
                        {category.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Business Description *
                  </label>
                  <textarea
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={5}
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                    placeholder="Describe your business, services, and what makes you unique..."
                    required
                  />
                  <p className="mt-1 text-sm text-slate-500">
                    {formData.description.length}/500 characters
                  </p>
                </div>

                <div className="flex justify-end">
                  <button
                    type="button"
                    onClick={() => setStep(2)}
                    disabled={!validateStep1()}
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-slate-300 disabled:cursor-not-allowed transition-colors"
                  >
                    Next Step
                  </button>
                </div>
              </div>
            )}

            {/* Step 2: Location */}
            {step === 2 && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-slate-900 mb-6">Location Information</h3>
                
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Street Address *
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                    placeholder="123 Main Street"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      City *
                    </label>
                    <input
                      type="text"
                      name="city"
                      value={formData.city}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                      placeholder="City"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      State *
                    </label>
                    <select
                      name="state"
                      value={formData.state}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                      required
                      title="Select your state"
                    >
                      <option value="">Select a state</option>
                      {INDIAN_STATES.map(state => (
                        <option key={state} value={state}>
                          {state}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      PIN Code
                    </label>
                    <input
                      type="text"
                      name="pincode"
                      value={formData.pincode}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                      placeholder="110001"
                      pattern="[0-9]{6}"
                      maxLength={6}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Country
                    </label>
                    <select
                      name="country"
                      value={formData.country}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                      title="Select country"
                    >
                      <option value="India">India</option>
                      <option value="United States">United States</option>
                      <option value="Canada">Canada</option>
                      <option value="United Kingdom">United Kingdom</option>
                      <option value="Australia">Australia</option>
                    </select>
                  </div>
                </div>

                {/* Location Coordinates Section */}
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-medium text-slate-900">📍 Map Location</h3>
                    <div className="flex gap-2">
                      <button
                        type="button"
                        onClick={handleGeocodeAddress}
                        disabled={geocoding || !formData.address || !formData.city}
                        className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-slate-300 disabled:cursor-not-allowed transition-colors text-sm flex items-center gap-2"
                      >
                        {geocoding ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            Getting Location...
                          </>
                        ) : (
                          <>
                            🗺️ Get Coordinates
                          </>
                        )}
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowMapPicker(!showMapPicker)}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm flex items-center gap-2"
                      >
                        {showMapPicker ? '🔽 Hide Map' : '🗺️ Pick on Map'}
                      </button>
                    </div>
                  </div>
                  
                  <p className="text-sm text-slate-600 mb-4">
                    We&apos;ll automatically find your location coordinates to show your listing on the map.
                    You can also enter them manually, or use the interactive map picker below.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Latitude
                      </label>
                      <input
                        type="number"
                        name="locationLat"
                        value={formData.locationLat || ''}
                        onChange={handleNumberInputChange}
                        className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                        placeholder="e.g., 28.6139"
                        step="any"
                        min="-90"
                        max="90"
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">
                        Longitude
                      </label>
                      <input
                        type="number"
                        name="locationLng"
                        value={formData.locationLng || ''}
                        onChange={handleNumberInputChange}
                        className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                        placeholder="e.g., 77.2090"
                        step="any"
                        min="-180"
                        max="180"
                      />
                    </div>
                  </div>

                  {/* Interactive Map Picker */}
                  {showMapPicker && (
                    <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                      <h4 className="text-md font-medium text-slate-900 mb-3">🗺️ Interactive Map Picker</h4>
                      <LocationPickerMap
                        initialLat={formData.locationLat || undefined}
                        initialLng={formData.locationLng || undefined}
                        onLocationSelect={handleMapLocationSelect}
                        className="w-full"
                      />
                    </div>
                  )}

                  {formData.locationLat && formData.locationLng && (
                    <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                      <div className="flex items-center gap-2 text-green-800 text-sm">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                        Coordinates set! Your listing will appear on the map.
                      </div>
                    </div>
                  )}
                </div>

                <div className="flex justify-between">
                  <button
                    type="button"
                    onClick={() => setStep(1)}
                    className="px-6 py-3 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors"
                  >
                    Previous
                  </button>
                  <button
                    type="button"
                    onClick={() => setStep(3)}
                    disabled={!validateStep2()}
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-slate-300 disabled:cursor-not-allowed transition-colors"
                  >
                    Next Step
                  </button>
                </div>
              </div>
            )}

            {/* Step 3: Contact & Tags */}
            {step === 3 && (
              <div className="space-y-6">
                <h3 className="text-xl font-bold text-slate-900 mb-6">Contact Information & Tags</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Phone Number
                    </label>
                    <input
                      type="tel"
                      name="phone"
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                      placeholder="+91 98765 43210"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Email Address
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Website
                  </label>                    <input
                      type="url"
                      name="website"
                      value={formData.website}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-slate-900 bg-white"
                      placeholder="https://www.yourbusiness.in"
                    />
                </div>

                {/* Tags Selection */}
                {tags.length > 0 && (
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-3">
                      Tags (Select all that apply)
                    </label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {tags.map(tag => (
                        <label key={tag.id} className="flex items-center space-x-2 cursor-pointer">
                          <input
                            type="checkbox"
                            checked={formData.tagIds.includes(tag.id)}
                            onChange={() => handleTagToggle(tag.id)}
                            className="w-4 h-4 text-blue-600 border-slate-300 rounded focus:ring-blue-500"
                          />
                          <span className="text-sm text-slate-700">{tag.name}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                )}

                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">📝 Review Before Submitting</h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Ensure all information is accurate and up-to-date</li>
                    <li>• Your listing will be reviewed before going live</li>
                    <li>• You can edit your listing anytime from your dashboard</li>
                  </ul>
                </div>

                <div className="flex justify-between">
                  <button
                    type="button"
                    onClick={() => setStep(2)}
                    className="px-6 py-3 border border-slate-300 text-slate-700 rounded-lg hover:bg-slate-50 transition-colors"
                  >
                    Previous
                  </button>
                  <button
                    type="submit"
                    disabled={loading}
                    className="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-slate-300 disabled:cursor-not-allowed transition-colors font-medium"
                  >
                    {loading ? "Creating Listing..." : "Create Listing"}
                  </button>
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Benefits Section */}
        <div className="max-w-3xl mx-auto mt-16">
          <h2 className="text-2xl font-bold text-slate-900 mb-8 text-center">
            Why List Your Business With Us?
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
              </div>
              <h3 className="font-bold text-slate-900 mb-2">Increased Visibility</h3>
              <p className="text-slate-600 text-sm">Get discovered by thousands of potential customers searching for businesses like yours.</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-emerald-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
              </div>
              <h3 className="font-bold text-slate-900 mb-2">Boost Sales</h3>
              <p className="text-slate-600 text-sm">Connect with local customers and grow your business with our platform.</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="font-bold text-slate-900 mb-2">Easy Management</h3>
              <p className="text-slate-600 text-sm">Simple tools to manage your listing, respond to customers, and track performance.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
