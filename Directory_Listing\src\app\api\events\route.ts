import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { getServerSession } from "next-auth";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const category = searchParams.get('category') || '';
    const cityId = searchParams.get('cityId') || '';
    const stateId = searchParams.get('stateId') || '';
    const search = searchParams.get('search') || '';
    const featured = searchParams.get('featured') === 'true';
    const upcoming = searchParams.get('upcoming') === 'true';
    const free = searchParams.get('free') === 'true';
    const paid = searchParams.get('paid') === 'true';
    const sort = searchParams.get('sort') || 'date';

    const skip = (page - 1) * limit;

    interface WhereConditions {
      status: 'APPROVED' | 'PENDING' | 'REJECTED';
      date?: {
        gte: Date;
      };
      category?: string;
      cityId?: string;
      stateId?: string;
      isFeatured?: boolean;
      price?: null | {
        not?: null;
        equals?: number;
        gt?: number;
      };
      OR?: Array<{
        title?: { contains: string; mode: 'insensitive' };
        description?: { contains: string; mode: 'insensitive' };
        location?: { contains: string; mode: 'insensitive' };
      }>;
    }

    const whereConditions: WhereConditions = {
      status: 'APPROVED'
    };

    if (upcoming) {
      whereConditions.date = { gte: new Date() };
    }

    if (category) {
      whereConditions.category = category;
    }

    if (cityId) {
      whereConditions.cityId = cityId;
    } else if (stateId) {
      whereConditions.stateId = stateId;
    }

    if (featured) {
      whereConditions.isFeatured = true;
    }

    if (free && !paid) {
      whereConditions.price = null;
    } else if (paid && !free) {
      whereConditions.price = { not: null };
    }

    if (search) {
      whereConditions.OR = [
        { title: { contains: search, mode: 'insensitive' } },
        { description: { contains: search, mode: 'insensitive' } },
        { location: { contains: search, mode: 'insensitive' } }
      ];
    }

    let orderBy: 
      | { date: 'asc' | 'desc' }
      | { createdAt: 'asc' | 'desc' }
      | { rsvps: { _count: 'asc' | 'desc' } } = { date: 'asc' };
    
    switch (sort) {
      case 'newest':
        orderBy = { createdAt: 'desc' };
        break;
      case 'popular':
        orderBy = { rsvps: { _count: 'desc' } };
        break;
      case 'date':
      default:
        orderBy = { date: 'asc' };
        break;
    }

    const [events, total] = await Promise.all([
      prisma.event.findMany({
        where: whereConditions,
        include: {
          createdBy: {
            select: {
              id: true,
              name: true,
              image: true
            }
          },
          cityRef: {
            select: {
              name: true,
              state: {
                select: {
                  name: true,
                  code: true
                }
              }
            }
          },
          _count: {
            select: {
              rsvps: true
            }
          }
        },
        orderBy,
        skip,
        take: limit
      }),
      prisma.event.count({ where: whereConditions })
    ]);

    return NextResponse.json({
      events,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error fetching events:', error);
    return NextResponse.json(
      { error: 'Failed to fetch events' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession();
    
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body = await req.json();
    const { title, description, date, location, state, city, category, price, image, lat, lng, cityId } = body;

    // Validate required fields
    if (!title || !description || !date || !location || !state || !city || !category) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get user
    const user = await prisma.user.findUnique({
      where: { email: session.user.email }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    const event = await prisma.event.create({
      data: {
        title,
        description,
        date: new Date(date),
        location,
        state,
        city,
        category,
        price: price ? parseFloat(price) : null,
        image,
        lat: lat ? parseFloat(lat) : null,
        lng: lng ? parseFloat(lng) : null,
        cityId,
        createdById: user.id,
        status: user.role === 'ADMIN' ? 'APPROVED' : 'PENDING'
      },
      include: {
        createdBy: {
          select: {
            id: true,
            name: true,
            image: true
          }
        },
        cityRef: {
          select: {
            name: true,
            state: {
              select: {
                name: true,
                code: true
              }
            }
          }
        }
      }
    });

    return NextResponse.json(event);
  } catch (error) {
    console.error('Error creating event:', error);
    return NextResponse.json(
      { error: 'Failed to create event' },
      { status: 500 }
    );
  }
}
