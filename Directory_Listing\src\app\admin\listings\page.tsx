"use client";
import { useEffect, useState } from "react";

interface User {
  id: string;
  name: string;
  email: string;
  status: 'ACTIVE' | 'BANNED';
}

interface Category {
  id: string;
  name: string;
}

interface Tag {
  id: string;
  name: string;
}

interface Listing {
  id: string;
  title: string;
  description: string;
  address: string;
  city: string;
  state: string;
  country: string;
  phone: string;
  email: string;
  website: string;
  categoryId: string;
  userId: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  category?: Category;
  tags?: Tag[];
  user?: User;
}

export default function AdminListingsPage() {
  const [listings, setListings] = useState<Listing[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [users, setUsers] = useState<User[]>([]);
  const [form, setForm] = useState({
    title: "",
    description: "",
    address: "",
    city: "",
    state: "",
    country: "",
    phone: "",
    email: "",
    website: "",
    categoryId: "",
    userId: "",
    tagIds: [] as string[],
  });
  const [loading, setLoading] = useState(false);
  const [editId, setEditId] = useState<string | null>(null);
  const [showEdit, setShowEdit] = useState(false);
  const [selectedListings, setSelectedListings] = useState<string[]>([]);
  const [bulkAction, setBulkAction] = useState("");

  useEffect(() => {
    fetchListings();
    fetchCategories();
    fetchTags();
    fetchUsers();
  }, []);

  async function fetchListings() {
    const res = await fetch("/api/admin/listings");
    const data = await res.json();
    setListings(data);
  }

  async function fetchCategories() {
    const res = await fetch("/api/admin/categories");
    const data = await res.json();
    setCategories(data);
  }

  async function fetchTags() {
    const res = await fetch("/api/admin/tags");
    const data = await res.json();
    setTags(data);
  }

  async function fetchUsers() {
    const res = await fetch("/api/admin/users");
    const data = await res.json();
    setUsers(data);
  }

  function handleChange(e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) {
    const { name, value } = e.target;
    const checked = (e.target as HTMLInputElement).checked;
    
    if (name === "tagIds") {
      let newTagIds = [...form.tagIds];
      if (checked) {
        newTagIds.push(value);
      } else {
        newTagIds = newTagIds.filter((id) => id !== value);
      }
      setForm({ ...form, tagIds: newTagIds });
    } else {
      setForm({ ...form, [name]: value });
    }
  }

  function handleBulkSelection(listingId: string) {
    if (selectedListings.includes(listingId)) {
      setSelectedListings(selectedListings.filter(id => id !== listingId));
    } else {
      setSelectedListings([...selectedListings, listingId]);
    }
  }

  function handleSelectAll() {
    if (selectedListings.length === listings.length) {
      setSelectedListings([]);
    } else {
      setSelectedListings(listings.map(listing => listing.id));
    }
  }

  async function handleBulkAction() {
    if (!bulkAction || selectedListings.length === 0) return;
    
    setLoading(true);
    await fetch("/api/admin/listings/bulk", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ action: bulkAction, listingIds: selectedListings }),
    });
    setSelectedListings([]);
    setBulkAction("");
    setLoading(false);
    fetchListings();
  }

  async function handleStatusChange(listingId: string, status: 'PENDING' | 'APPROVED' | 'REJECTED') {
    setLoading(true);
    await fetch("/api/admin/listings/approve", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ listingId, status }),
    });
    setLoading(false);
    fetchListings();
  }

  function resetForm() {
    setForm({
      title: "",
      description: "",
      address: "",
      city: "",
      state: "",
      country: "",
      phone: "",
      email: "",
      website: "",
      categoryId: "",
      userId: "",
      tagIds: [],
    });
  }

  async function addListing(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    await fetch("/api/admin/listings", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ ...form, tagIds: form.tagIds }),
    });
    resetForm();
    setLoading(false);
    fetchListings();
  }

  function startEdit(listing: Listing) {
    setEditId(listing.id);
    setForm({
      title: listing.title || "",
      description: listing.description || "",
      address: listing.address || "",
      city: listing.city || "",
      state: listing.state || "",
      country: listing.country || "",
      phone: listing.phone || "",
      email: listing.email || "",
      website: listing.website || "",
      categoryId: listing.categoryId || "",
      userId: listing.userId || "",
      tagIds: listing.tags?.map((t: Tag) => t.id) || [],
    });
    setShowEdit(true);
  }

  async function updateListing(e: React.FormEvent) {
    e.preventDefault();
    if (!editId) return;
    setLoading(true);
    await fetch("/api/admin/listings", {
      method: "PUT",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ ...form, id: editId, tagIds: form.tagIds }),
    });
    setEditId(null);
    setShowEdit(false);
    resetForm();
    setLoading(false);
    fetchListings();
  }

  async function deleteListing(id: string) {
    setLoading(true);
    await fetch("/api/admin/listings", {
      method: "DELETE",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ id }),
    });
    setLoading(false);
    fetchListings();
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6 text-gray-900">Manage Listings</h1>
      
      {/* Bulk Actions */}
      {selectedListings.length > 0 && (
        <div className="mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div className="flex items-center gap-4">
            <span className="text-sm text-gray-900">
              {selectedListings.length} listing{selectedListings.length > 1 ? 's' : ''} selected
            </span>
            <select 
              value={bulkAction} 
              onChange={(e) => setBulkAction(e.target.value)}
              className="px-3 py-1 border rounded text-sm"
              title="Select bulk action"
            >
              <option value="">Select Action</option>
              <option value="approve">Approve</option>
              <option value="reject">Reject</option>
              <option value="delete">Delete</option>
            </select>
            <button 
              onClick={handleBulkAction}
              disabled={!bulkAction || loading}
              className="px-4 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50"
            >
              Apply
            </button>
            <button 
              onClick={() => setSelectedListings([])}
              className="px-4 py-1 bg-gray-400 text-white rounded text-sm hover:bg-gray-500"
            >
              Clear Selection
            </button>
          </div>
        </div>
      )}

      <form onSubmit={addListing} className="mb-6 bg-gray-100 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-4 text-gray-900">Add New Listing</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <input 
            name="title" 
            value={form.title} 
            onChange={handleChange} 
            placeholder="Title" 
            className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
            required 
          />
          <textarea 
            name="description" 
            value={form.description} 
            onChange={handleChange} 
            placeholder="Description" 
            className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
            rows={3}
          />
          <input 
            name="address" 
            value={form.address} 
            onChange={handleChange} 
            placeholder="Address" 
            className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
          />
          <input 
            name="city" 
            value={form.city} 
            onChange={handleChange} 
            placeholder="City" 
            className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
            required 
          />
          <input 
            name="state" 
            value={form.state} 
            onChange={handleChange} 
            placeholder="State" 
            className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
          />
          <input 
            name="country" 
            value={form.country} 
            onChange={handleChange} 
            placeholder="Country" 
            className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
          />
          <input 
            name="phone" 
            value={form.phone} 
            onChange={handleChange} 
            placeholder="Phone" 
            className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
          />
          <input 
            name="email" 
            value={form.email} 
            onChange={handleChange} 
            placeholder="Email" 
            className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
          />
          <input 
            name="website" 
            value={form.website} 
            onChange={handleChange} 
            placeholder="Website" 
            className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
          />
          <select 
            name="categoryId" 
            value={form.categoryId} 
            onChange={handleChange} 
            className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
            required
            title="Select category"
          >
            <option value="">Select Category</option>
            {categories.map((cat) => (
              <option key={cat.id} value={cat.id}>{cat.name}</option>
            ))}
          </select>
          <select 
            name="userId" 
            value={form.userId} 
            onChange={handleChange} 
            className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
            required
            title="Select user"
          >
            <option value="">Select User</option>
            {users.map((user) => (
              <option key={user.id} value={user.id}>{user.name} ({user.email})</option>
            ))}
          </select>
        </div>
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-900 mb-2">Tags</label>
          <div className="flex flex-wrap gap-3">
            {tags.map((tag) => (
              <label key={tag.id} className="flex items-center gap-2 cursor-pointer">
                <input
                  type="checkbox"
                  name="tagIds"
                  value={tag.id}
                  checked={form.tagIds.includes(tag.id)}
                  onChange={handleChange}
                  className="rounded focus:ring-2 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-900">{tag.name}</span>
              </label>
            ))}
          </div>
        </div>
        <div className="mt-4">
          <button 
            type="submit" 
            className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50" 
            disabled={loading}
          >
            {loading ? 'Adding...' : 'Add Listing'}
          </button>
        </div>
      </form>
      {/* Edit Modal */}
      {showEdit && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold mb-4 text-gray-900">Edit Listing</h2>
            <form onSubmit={updateListing}>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <input 
                  name="title" 
                  value={form.title} 
                  onChange={handleChange} 
                  placeholder="Title" 
                  className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
                  required 
                />
                <textarea 
                  name="description" 
                  value={form.description} 
                  onChange={handleChange} 
                  placeholder="Description" 
                  className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
                  rows={3}
                />
                <input 
                  name="address" 
                  value={form.address} 
                  onChange={handleChange} 
                  placeholder="Address" 
                  className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
                />
                <input 
                  name="city" 
                  value={form.city} 
                  onChange={handleChange} 
                  placeholder="City" 
                  className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
                  required 
                />
                <input 
                  name="state" 
                  value={form.state} 
                  onChange={handleChange} 
                  placeholder="State" 
                  className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
                />
                <input 
                  name="country" 
                  value={form.country} 
                  onChange={handleChange} 
                  placeholder="Country" 
                  className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
                />
                <input 
                  name="phone" 
                  value={form.phone} 
                  onChange={handleChange} 
                  placeholder="Phone" 
                  className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
                />
                <input 
                  name="email" 
                  value={form.email} 
                  onChange={handleChange} 
                  placeholder="Email" 
                  className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
                />
                <input 
                  name="website" 
                  value={form.website} 
                  onChange={handleChange} 
                  placeholder="Website" 
                  className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
                />
                <select 
                  name="categoryId" 
                  value={form.categoryId} 
                  onChange={handleChange} 
                  className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
                  required
                  title="Select category"
                >
                  <option value="">Select Category</option>
                  {categories.map((cat) => (
                    <option key={cat.id} value={cat.id}>{cat.name}</option>
                  ))}
                </select>
                <select 
                  name="userId" 
                  value={form.userId} 
                  onChange={handleChange} 
                  className="px-3 py-2 border rounded focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900" 
                  required
                  title="Select user"
                >
                  <option value="">Select User</option>
                  {users.map((user) => (
                    <option key={user.id} value={user.id}>{user.name} ({user.email})</option>
                  ))}
                </select>
              </div>
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-900 mb-2">Tags</label>
                <div className="flex flex-wrap gap-3">
                  {tags.map((tag) => (
                    <label key={tag.id} className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="checkbox"
                        name="tagIds"
                        value={tag.id}
                        checked={form.tagIds.includes(tag.id)}
                        onChange={handleChange}
                        className="rounded focus:ring-2 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-900">{tag.name}</span>
                    </label>
                  ))}
                </div>
              </div>
              <div className="flex gap-3 mt-6">
                <button 
                  type="submit" 
                  className="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50" 
                  disabled={loading}
                >
                  {loading ? 'Updating...' : 'Update'}
                </button>
                <button 
                  type="button" 
                  className="px-6 py-2 bg-gray-400 text-white rounded hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-gray-500" 
                  onClick={() => { setShowEdit(false); setEditId(null); }}
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      
      {/* Listings Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="p-3 text-left">
                  <input
                    type="checkbox"
                    checked={selectedListings.length === listings.length && listings.length > 0}
                    onChange={handleSelectAll}
                    className="rounded focus:ring-2 focus:ring-blue-500"
                    title="Select all listings"
                  />
                </th>
                <th className="p-3 text-left font-medium text-gray-700">Title</th>
                <th className="p-3 text-left font-medium text-gray-700">Category</th>
                <th className="p-3 text-left font-medium text-gray-700">User</th>
                <th className="p-3 text-left font-medium text-gray-700">City</th>
                <th className="p-3 text-left font-medium text-gray-700">Status</th>
                <th className="p-3 text-left font-medium text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              {listings.map(listing => (
                <tr key={listing.id} className="hover:bg-gray-50">
                  <td className="p-3">
                    <input
                      type="checkbox"
                      checked={selectedListings.includes(listing.id)}
                      onChange={() => handleBulkSelection(listing.id)}
                      className="rounded focus:ring-2 focus:ring-blue-500"
                      title={`Select ${listing.title}`}
                    />
                  </td>
                  <td className="p-3">
                    <div className="font-medium text-gray-900">{listing.title}</div>
                    <div className="text-sm text-gray-500 truncate max-w-xs">{listing.description}</div>
                  </td>
                  <td className="p-3">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {listing.category?.name || 'No Category'}
                    </span>
                  </td>
                  <td className="p-3">
                    <div className="text-sm text-gray-900">{listing.user?.name || 'Unknown'}</div>
                    <div className="text-xs text-gray-500">{listing.user?.email || 'No email'}</div>
                  </td>
                  <td className="p-3 text-gray-900">{listing.city}</td>
                  <td className="p-3">
                    <select
                      value={listing.status}
                      onChange={(e) => handleStatusChange(listing.id, e.target.value as 'PENDING' | 'APPROVED' | 'REJECTED')}
                      className={`text-xs px-2 py-1 rounded-full border-0 focus:ring-2 focus:ring-blue-500 ${
                        listing.status === 'APPROVED' ? 'bg-green-100 text-green-800' :
                        listing.status === 'REJECTED' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}
                      title="Change status"
                    >
                      <option value="PENDING">Pending</option>
                      <option value="APPROVED">Approved</option>
                      <option value="REJECTED">Rejected</option>
                    </select>
                  </td>
                  <td className="p-3">
                    <div className="flex items-center gap-2">
                      <button 
                        className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50" 
                        onClick={() => startEdit(listing)} 
                        disabled={loading}
                      >
                        Edit
                      </button>
                      <button 
                        className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50" 
                        onClick={() => {
                          if (confirm('Are you sure you want to delete this listing?')) {
                            deleteListing(listing.id);
                          }
                        }} 
                        disabled={loading}
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {listings.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-500">No listings found</div>
          </div>
        )}
      </div>
    </div>
  );
} 