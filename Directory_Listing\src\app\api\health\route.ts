import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

export async function GET() {
  try {
    // Check database connection
    await prisma.$connect();
    
    // Simple database query to verify functionality
    const userCount = await prisma.user.count();
    const listingCount = await prisma.listing.count();
    
    const healthStatus = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      database: {
        connected: true,
        users: userCount,
        listings: listingCount,
      },
      services: {
        geocoding: !!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY,
        authentication: !!process.env.NEXTAUTH_SECRET,
      },
      version: process.env.npm_package_version || "unknown",
    };

    return NextResponse.json(healthStatus, { status: 200 });
  } catch (error) {
    console.error("Health check failed:", error);
    
    const errorStatus = {
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      error: process.env.NODE_ENV === 'development' ? error : "Service unavailable",
      database: {
        connected: false,
      },
    };

    return NextResponse.json(errorStatus, { status: 503 });
  } finally {
    await prisma.$disconnect();
  }
}
