"use client";
import { useEffect, useRef } from "react";
import L from "leaflet";
import "leaflet/dist/leaflet.css";
import { Event } from "@/types/events";

// Extend window interface for our custom function
declare global {
  interface Window {
    selectEvent?: (eventId: string) => void;
  }
}

// Fix for default markers in Leaflet
delete (L.Icon.Default.prototype as unknown as { _getIconUrl?: unknown })._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png",
  iconUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png",
  shadowUrl: "https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png",
});

interface EventsMapProps {
  events: Event[];
  onEventSelect: (event: Event) => void;
}

export default function EventsMap({ events, onEventSelect }: EventsMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<L.Map | null>(null);
  const markersRef = useRef<L.LayerGroup | null>(null);

  useEffect(() => {
    if (!mapRef.current || mapInstanceRef.current) return;

    // Initialize map
    const map = L.map(mapRef.current, {
      zoomControl: true,
      scrollWheelZoom: true,
    }).setView([20.5937, 78.9629], 5); // Center of India

    L.tileLayer("https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png", {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
    }).addTo(map);

    mapInstanceRef.current = map;
    markersRef.current = L.layerGroup().addTo(map);

    // Ensure map container has low z-index
    if (mapRef.current) {
      mapRef.current.style.zIndex = '1';
    }

    return () => {
      map.remove();
      mapInstanceRef.current = null;
    };
  }, []);

  useEffect(() => {
    if (!mapInstanceRef.current || !markersRef.current) return;

    // Clear existing markers
    markersRef.current.clearLayers();

    const validEvents = events.filter(event => event.lat && event.lng);

    if (validEvents.length === 0) return;

    // Add markers for events
    validEvents.forEach((event) => {
      if (!event.lat || !event.lng) return;

      // Create custom icon with event category color
      const categoryColors: { [key: string]: string } = {
        Technology: "#8B5CF6",
        Business: "#3B82F6",
        Arts: "#F59E0B",
        Music: "#EF4444",
        Sports: "#10B981",
        Food: "#F97316",
        Education: "#6366F1",
        Health: "#EC4899",
        Entertainment: "#8B5CF6",
        Fashion: "#F59E0B",
        Travel: "#06B6D4"
      };

      const color = categoryColors[event.category] || "#8B5CF6";

      const customIcon = L.divIcon({
        html: `
          <div style="
            background-color: ${color};
            width: 30px;
            height: 30px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
          ">
            📅
          </div>
        `,
        className: 'custom-event-marker',
        iconSize: [30, 30],
        iconAnchor: [15, 15]
      });

      const marker = L.marker([event.lat, event.lng], { icon: customIcon })
        .bindPopup(`
          <div style="min-width: 250px; max-width: 300px;">
            <div style="
              padding: 12px 0 8px 0;
              border-bottom: 1px solid #e5e7eb;
              margin-bottom: 8px;
            ">
              <div style="
                background: linear-gradient(135deg, ${color}, ${color}99);
                color: white;
                padding: 4px 8px;
                border-radius: 12px;
                font-size: 11px;
                font-weight: 600;
                display: inline-block;
                margin-bottom: 8px;
              ">
                ${event.category}
              </div>
              <h3 style="
                margin: 0;
                font-size: 16px;
                font-weight: bold;
                color: #1f2937;
                line-height: 1.3;
              ">
                ${event.title}
              </h3>
            </div>
            
            <div style="margin-bottom: 12px;">
              <p style="
                margin: 0 0 8px 0;
                color: #6b7280;
                font-size: 13px;
                line-height: 1.4;
              ">
                ${event.description.length > 100 ? event.description.substring(0, 100) + '...' : event.description}
              </p>
              
              <div style="display: flex; flex-direction: column; gap: 4px; font-size: 12px; color: #6b7280;">
                <div style="display: flex; align-items: center; gap: 6px;">
                  <span>📅</span>
                  <span>${new Date(event.date).toLocaleDateString()}</span>
                </div>
                <div style="display: flex; align-items: center; gap: 6px;">
                  <span>📍</span>
                  <span>${event.city}, ${event.state}</span>
                </div>
                <div style="display: flex; align-items: center; gap: 6px;">
                  <span>👥</span>
                  <span>${event._count.rsvps} interested</span>
                </div>
                <div style="display: flex; align-items: center; gap: 6px;">
                  <span>💰</span>
                  <span>${event.price ? `₹${event.price}` : 'Free'}</span>
                </div>
              </div>
            </div>
            
            <button 
              onclick="window.selectEvent('${event.id}')"
              style="
                width: 100%;
                padding: 8px 16px;
                background: linear-gradient(135deg, #8B5CF6, #3B82F6);
                color: white;
                border: none;
                border-radius: 8px;
                font-weight: 600;
                font-size: 13px;
                cursor: pointer;
                transition: all 0.2s;
              "
              onmouseover="this.style.transform='translateY(-1px)'"
              onmouseout="this.style.transform='translateY(0)'"
            >
              View Details
            </button>
          </div>
        `, {
          maxWidth: 300,
          className: 'custom-popup'
        });

      markersRef.current?.addLayer(marker);
    });

    // Fit map to show all markers
    if (validEvents.length > 0) {
      const group = L.featureGroup(markersRef.current!.getLayers());
      mapInstanceRef.current!.fitBounds(group.getBounds().pad(0.1));
    }

    // Add global function to handle event selection from popup
    if (typeof window !== 'undefined') {
      window.selectEvent = (eventId: string) => {
        const event = events.find(e => e.id === eventId);
        if (event) {
          onEventSelect(event);
        }
      };
    }

    return () => {
      if (typeof window !== 'undefined') {
        delete window.selectEvent;
      }
    };
  }, [events, onEventSelect]);

  return (
    <div 
      ref={mapRef} 
      className="w-full h-full rounded-2xl overflow-hidden relative z-0 min-h-[400px]"
    />
  );
}
