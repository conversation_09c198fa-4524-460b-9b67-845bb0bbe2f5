import React, { useEffect, useRef, useState } from 'react';

interface LocationPickerMapProps {
  initialLat?: number;
  initialLng?: number;
  onLocationSelect: (lat: number, lng: number) => void;
  className?: string;
}

const LocationPickerMap: React.FC<LocationPickerMapProps> = ({
  initialLat = 20.5937,
  initialLng = 78.9629,
  onLocationSelect,
  className = ''
}) => {
  const mapRef = useRef<HTMLDivElement>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const mapInstanceRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const markerRef = useRef<any>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const leafletRef = useRef<any>(null);
  const [isMapReady, setIsMapReady] = useState(false);

  useEffect(() => {
    const initializeMap = async () => {
      try {
        // Dynamically import Leaflet
        const leafletModule = await import('leaflet');
        const leaflet = leafletModule.default;
        leafletRef.current = leaflet;

        // Load CSS
        if (!document.querySelector('link[href*="leaflet.css"]')) {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
          document.head.appendChild(link);
        }

        // Fix default marker icons
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        delete (leaflet.Icon.Default.prototype as any)._getIconUrl;
        leaflet.Icon.Default.mergeOptions({
          iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
          iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
          shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
        });

        if (mapRef.current && !mapInstanceRef.current) {
          // Determine initial position
          const centerLat = initialLat || 20.5937; // Center of India
          const centerLng = initialLng || 78.9629;
          const zoom = initialLat && initialLng ? 13 : 5;

          // Initialize map
          const map = leaflet.map(mapRef.current, {
            zoomControl: true,
            scrollWheelZoom: true,
            doubleClickZoom: true,
            touchZoom: true,
          }).setView([centerLat, centerLng], zoom);

          // Add tile layer
          leaflet.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: 19,
          }).addTo(map);

          // Add initial marker if coordinates provided
          if (initialLat && initialLng) {
            const marker = leaflet.marker([initialLat, initialLng], {
              draggable: true
            }).addTo(map);

            marker.bindPopup('📍 Selected Location<br><small>Drag to adjust or click elsewhere</small>').openPopup();
            
            // Handle marker drag
            marker.on('dragend', function() {
              const position = marker.getLatLng();
              onLocationSelect(position.lat, position.lng);
            });

            markerRef.current = marker;
          }

          // Handle map clicks
          map.on('click', function(e: { latlng: { lat: number; lng: number } }) {
            const lat = e.latlng.lat;
            const lng = e.latlng.lng;

            // Remove existing marker
            if (markerRef.current) {
              map.removeLayer(markerRef.current);
            }

            // Add new marker
            const marker = leaflet.marker([lat, lng], {
              draggable: true
            }).addTo(map);

            marker.bindPopup('📍 Selected Location<br><small>Drag to adjust or click elsewhere</small>').openPopup();
            
            // Handle marker drag
            marker.on('dragend', function() {
              const position = marker.getLatLng();
              onLocationSelect(position.lat, position.lng);
            });

            markerRef.current = marker;
            onLocationSelect(lat, lng);
          });

          mapInstanceRef.current = map;
          setIsMapReady(true);
        }
      } catch (error) {
        console.error('Error initializing map:', error);
      }
    };

    initializeMap();

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
        mapInstanceRef.current = null;
      }
    };
  }, [initialLat, initialLng, onLocationSelect]);

  // Update marker when coordinates change externally
  useEffect(() => {
    if (mapInstanceRef.current && initialLat && initialLng && isMapReady && leafletRef.current) {
      const leaflet = leafletRef.current;

      // Remove existing marker
      if (markerRef.current) {
        mapInstanceRef.current.removeLayer(markerRef.current);
      }

      // Add new marker
      const marker = leaflet.marker([initialLat, initialLng], {
        draggable: true
      }).addTo(mapInstanceRef.current);

      marker.bindPopup('📍 Selected Location<br><small>Drag to adjust or click elsewhere</small>');
      
      // Handle marker drag
      marker.on('dragend', function() {
        const position = marker.getLatLng();
        onLocationSelect(position.lat, position.lng);
      });

      markerRef.current = marker;

      // Center map on new location
      mapInstanceRef.current.setView([initialLat, initialLng], 13);
    }
  }, [initialLat, initialLng, isMapReady, onLocationSelect]);

  const getCurrentLocation = () => {
    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const lat = position.coords.latitude;
          const lng = position.coords.longitude;
          onLocationSelect(lat, lng);
          
          if (mapInstanceRef.current) {
            mapInstanceRef.current.setView([lat, lng], 15);
          }
        },
        (error) => {
          console.error('Error getting location:', error);
          alert('Could not get your current location. Please click on the map to select your location.');
        }
      );
    } else {
      alert('Geolocation is not supported by this browser. Please click on the map to select your location.');
    }
  };

  return (
    <div className={`relative ${className}`}>
      <div className="mb-3 flex flex-wrap gap-2">
        <button
          type="button"
          onClick={getCurrentLocation}
          className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm flex items-center gap-2"
        >
          📍 Use My Location
        </button>
        <span className="text-sm text-slate-600 self-center">
          or click anywhere on the map to set location
        </span>
      </div>
      
      <div
        ref={mapRef}
        className="w-full h-64 border border-slate-300 rounded-lg bg-slate-100"
      />
      
      <div className="mt-2 text-xs text-slate-500">
        💡 <strong>Tip:</strong> Click on the map to place a marker, or drag the marker to adjust the location precisely.
      </div>
    </div>
  );
};

export default LocationPickerMap;
