import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdmin } from "@/lib/auth-middleware";
import { Prisma } from "@prisma/client";

export async function GET(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const { searchParams } = new URL(req.url);
    const search = searchParams.get('search') || '';
    const role = searchParams.get('role') || '';
    const status = searchParams.get('status') || '';
    
    const whereConditions: Prisma.UserWhereInput = {};
    
    if (search) {
      // For SQLite, use contains without mode parameter
      const searchLower = search.toLowerCase();
      whereConditions.OR = [
        { name: { contains: searchLower } },
        { email: { contains: searchLower } }
      ];
    }
    
    if (role && ['USER', 'ADMIN', 'MODERATOR'].includes(role)) {
      whereConditions.role = role as 'USER' | 'ADMIN' | 'MODERATOR';
    }
    
    if (status && ['ACTIVE', 'BANNED'].includes(status)) {
      whereConditions.status = status as 'ACTIVE' | 'BANNED';
    }

    const users = await prisma.user.findMany({
      where: whereConditions,
      include: {
        _count: {
          select: {
            listings: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Remove sensitive data before sending
    const sanitizedUsers = users.map(user => ({
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      status: user.status,
      image: user.image,
      listingCount: user._count.listings,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    }));

    return NextResponse.json(sanitizedUsers);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    );
  }
}

export async function PUT(req: NextRequest) {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const { userId, role, status } = await req.json();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      );
    }
    
    const updateData: {
      role?: 'USER' | 'ADMIN' | 'MODERATOR';
      status?: 'ACTIVE' | 'BANNED';
    } = {};
    
    if (role && ['USER', 'ADMIN', 'MODERATOR'].includes(role)) {
      updateData.role = role as 'USER' | 'ADMIN' | 'MODERATOR';
    }
    
    if (status && ['ACTIVE', 'BANNED'].includes(status)) {
      updateData.status = status as 'ACTIVE' | 'BANNED';
    }
    
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        { error: 'No valid update data provided' },
        { status: 400 }
      );
    }

    const user = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        role: true,
        status: true,
        image: true,
        createdAt: true,
        updatedAt: true
      }
    });

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    );
  }
}
