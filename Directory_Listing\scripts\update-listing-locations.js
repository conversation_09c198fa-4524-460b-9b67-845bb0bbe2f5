const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateListingsWithLocationRefs() {
  try {
    console.log('🔄 Starting to update listings with location references...');
    
    // Get all listings
    const listings = await prisma.listing.findMany({
      where: {
        OR: [
          { cityId: null },
          { stateId: null }
        ]
      }
    });
    
    console.log(`📊 Found ${listings.length} listings to update`);
    
    // Get all states and cities
    const states = await prisma.state.findMany({
      include: {
        cities: true
      }
    });
    
    let updatedCount = 0;
    
    for (const listing of listings) {
      // Find matching state
      const matchingState = states.find(state => 
        state.name.toLowerCase() === listing.state.toLowerCase()
      );
      
      if (matchingState) {
        // Handle city name aliases
        let cityToMatch = listing.city.toLowerCase();
        if (cityToMatch === 'bangalore') cityToMatch = 'bengaluru';
        if (cityToMatch === 'bombay') cityToMatch = 'mumbai';
        if (cityToMatch === 'calcutta') cityToMatch = 'kolkata';
        if (cityToMatch === 'madras') cityToMatch = 'chennai';
        
        // Find matching city within the state
        const matchingCity = matchingState.cities.find(city =>
          city.name.toLowerCase() === cityToMatch
        );
        
        if (matchingCity) {
          // Update listing with cityId and stateId
          await prisma.listing.update({
            where: { id: listing.id },
            data: {
              cityId: matchingCity.id,
              stateId: matchingState.id
            }
          });
          
          console.log(`✅ Updated listing "${listing.title}" with ${matchingCity.name}, ${matchingState.name}`);
          updatedCount++;
        } else {
          console.log(`⚠️  No matching city found for "${listing.city}" (searched as "${cityToMatch}") in state "${listing.state}"`);
        }
      } else {
        console.log(`⚠️  No matching state found for "${listing.state}"`);
      }
    }
    
    console.log(`🎉 Successfully updated ${updatedCount} listings with location references!`);
    
  } catch (error) {
    console.error('❌ Error updating listings:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateListingsWithLocationRefs();
