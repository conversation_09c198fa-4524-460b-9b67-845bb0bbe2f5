"use client";
import Link from "next/link";
import { useSession, signOut } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import NotificationBell from "@/components/NotificationBell";

export default function DashboardLayout({ children }: { children: React.ReactNode }) {
  const { data: session, status } = useSession();
  const router = useRouter();

  useEffect(() => {
    if (status === "unauthenticated") {
      router.push("/signup");
    }
  }, [status, router]);

  if (status === "loading") {
    return <div>Loading...</div>;
  }

  if (!session) {
    return null;
  }

  const handleLogout = async () => {
    await signOut({ 
      redirect: false,
      callbackUrl: "/" 
    });
    router.push("/");
  };

  return (
    <div className="flex min-h-screen">
      <aside className="w-56 bg-blue-700 text-white flex flex-col p-4 space-y-4">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-bold">User Dashboard</h2>
          <NotificationBell />
        </div>
        <nav className="flex flex-col space-y-2">
          <Link href="/dashboard">My Listings</Link>
          <Link href="/dashboard/add">Add Listing</Link>
          <Link href="/dashboard/profile">Profile</Link>
          <button 
            onClick={handleLogout}
            className="text-left hover:bg-blue-600 p-2 rounded transition-colors"
          >
            Logout
          </button>
        </nav>
      </aside>
      <main className="flex-1 bg-gray-50 p-8">{children}</main>
    </div>
  );
} 