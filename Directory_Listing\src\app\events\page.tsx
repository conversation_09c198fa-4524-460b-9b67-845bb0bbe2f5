"use client";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { 
  Calendar, 
  MapPin, 
  Search, 
  Filter, 
  Grid,
  Map,
  Plus,
  Heart,
  Share2,
  Clock,
  Users,
  Eye,
  X,
  ChevronLeft,
  ChevronRight
} from "lucide-react";
import { useSession } from "next-auth/react";
import Link from "next/link";
import Image from "next/image";
import dynamic from "next/dynamic";
import { Event, EventRSVP } from "@/types/events";

// Dynamically import EventsMap to prevent SSR issues
const EventsMap = dynamic(() => import("@/components/EventsMap"), {
  ssr: false,
  loading: () => <div className="flex items-center justify-center h-full">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
  </div>
});

interface State {
  id: string;
  name: string;
  code: string;
}

interface City {
  id: string;
  name: string;
  stateId: string;
}

const categories = [
  "Technology", "Business", "Arts", "Music", "Sports", "Food", 
  "Education", "Health", "Entertainment", "Fashion", "Travel"
];

export default function EventsPage() {
  const { data: session } = useSession();
  const [events, setEvents] = useState<Event[]>([]);
  const [featuredEvents, setFeaturedEvents] = useState<Event[]>([]);
  const [states, setStates] = useState<State[]>([]);
  const [cities, setCities] = useState<City[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  
  // Filters
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedState, setSelectedState] = useState("");
  const [selectedCity, setSelectedCity] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [freeOnly, setFreeOnly] = useState(false);
  const [paidOnly, setPaidOnly] = useState(false);
  const [sortBy, setSortBy] = useState("date");
  
  // UI State
  const [viewMode, setViewMode] = useState<"grid" | "map">("grid");
  const [showFilters, setShowFilters] = useState(false);
  const [currentSlide, setCurrentSlide] = useState(0);
  const [likedEvents, setLikedEvents] = useState<Set<string>>(new Set());
  const [heartAnimations, setHeartAnimations] = useState<Set<string>>(new Set());
  const [toast, setToast] = useState<{ message: string; type: 'success' | 'error' } | null>(null);

  const showToast = (message: string, type: 'success' | 'error' = 'success') => {
    setToast({ message, type });
    setTimeout(() => setToast(null), 3000);
  };

  useEffect(() => {
    fetchEvents();
    fetchFeaturedEvents();
    fetchStates();
    fetchCities();
    if (session) {
      fetchUserRSVPs();
    }
  }, [searchTerm, selectedState, selectedCity, selectedCategory, freeOnly, paidOnly, sortBy, session]);

  const fetchUserRSVPs = async () => {
    if (!session?.user?.email) return;
    
    try {
      const response = await fetch('/api/user/rsvps');
      if (response.ok) {
        const data = await response.json();
        const userLikedEvents = new Set<string>(data.rsvps.map((rsvp: EventRSVP) => rsvp.eventId));
        setLikedEvents(userLikedEvents);
      }
    } catch (error) {
      console.error('Error fetching user RSVPs:', error);
    }
  };

  const fetchEvents = async () => {
    try {
      const params = new URLSearchParams();
      if (searchTerm) params.append('search', searchTerm);
      if (selectedState) params.append('stateId', selectedState);
      if (selectedCity) params.append('cityId', selectedCity);
      if (selectedCategory) params.append('category', selectedCategory);
      if (freeOnly) params.append('free', 'true');
      if (paidOnly) params.append('paid', 'true');
      if (sortBy) params.append('sort', sortBy);
      params.append('upcoming', 'true');

      const response = await fetch(`/api/events?${params}`);
      if (response.ok) {
        const data = await response.json();
        setEvents(data.events);
      }
    } catch (error) {
      console.error('Error fetching events:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchFeaturedEvents = async () => {
    try {
      const response = await fetch('/api/events?featured=true&upcoming=true&limit=6');
      if (response.ok) {
        const data = await response.json();
        setFeaturedEvents(data.events);
      }
    } catch (error) {
      console.error('Error fetching featured events:', error);
    }
  };

  const fetchStates = async () => {
    try {
      const response = await fetch('/api/admin/locations/states');
      if (response.ok) {
        const data = await response.json();
        setStates(data);
      }
    } catch (error) {
      console.error('Error fetching states:', error);
    }
  };

  const fetchCities = async () => {
    try {
      const response = await fetch('/api/admin/locations/cities');
      if (response.ok) {
        const data = await response.json();
        setCities(data);
      }
    } catch (error) {
      console.error('Error fetching cities:', error);
    }
  };

  const handleRSVP = async (eventId: string, status: string = 'INTERESTED') => {
    if (!session) {
      // Redirect to login (only on client side)
      if (typeof window !== 'undefined') {
        window.location.href = '/signup';
      }
      return;
    }

    // Check if already liked to toggle
    const isCurrentlyLiked = likedEvents.has(eventId);
    
    // Always trigger heart animation for heart button clicks
    if (status === 'INTERESTED') {
      setHeartAnimations(prev => new Set([...prev, eventId]));
      
      // Toggle like state
      if (isCurrentlyLiked) {
        setLikedEvents(prev => {
          const newSet = new Set(prev);
          newSet.delete(eventId);
          return newSet;
        });
      } else {
        setLikedEvents(prev => new Set([...prev, eventId]));
      }
      
      // Remove animation after delay
      setTimeout(() => {
        setHeartAnimations(prev => {
          const newSet = new Set(prev);
          newSet.delete(eventId);
          return newSet;
        });
      }, 600);
    }

    try {
      // Send appropriate request based on current state
      if (status === 'INTERESTED' && isCurrentlyLiked) {
        // Remove RSVP
        const response = await fetch(`/api/events/${eventId}/rsvp`, {
          method: 'DELETE',
          headers: { 'Content-Type': 'application/json' }
        });
        if (response.ok) {
          showToast('Removed from your interests', 'success');
          fetchEvents();
        } else {
          showToast('Failed to remove interest', 'error');
        }
      } else {
        // Add RSVP
        const response = await fetch(`/api/events/${eventId}/rsvp`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ status })
        });
        if (response.ok) {
          const statusMessages = {
            'INTERESTED': 'Added to your interests! 💜',
            'GOING': 'Marked as going! See you there! 🎉',
            'NOT_GOING': 'Marked as not going'
          };
          showToast(statusMessages[status as keyof typeof statusMessages] || 'RSVP updated!', 'success');
          fetchEvents();
          
          // For modal RSVPs, update the liked events state if it's INTERESTED
          if (status === 'INTERESTED') {
            setLikedEvents(prev => new Set([...prev, eventId]));
          }
        } else {
          showToast('Failed to update RSVP', 'error');
        }
      }
    } catch (error) {
      console.error('Error updating RSVP:', error);
      showToast('Failed to update RSVP', 'error');
    }
  };

  const handleShare = async (event: Event) => {
    if (typeof window === 'undefined') return;
    
    if (typeof navigator !== 'undefined' && navigator.share) {
      try {
        await navigator.share({
          title: event.title,
          text: event.description,
          url: `${window.location.origin}/events/${event.id}`
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else if (typeof navigator !== 'undefined' && navigator.clipboard) {
      // Fallback to copying URL
      navigator.clipboard.writeText(`${window.location.origin}/events/${event.id}`);
      alert('Event URL copied to clipboard!');
    }
  };

  const filteredCities = selectedState 
    ? cities.filter(city => city.stateId === selectedState)
    : cities;

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % featuredEvents.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + featuredEvents.length) % featuredEvents.length);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-indigo-900">
      
      {/* Hero Section */}
      <motion.section 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative overflow-hidden bg-gradient-to-r from-purple-600 via-blue-600 to-teal-500 py-20"
      >
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative container mx-auto px-4">
          <div className="text-center text-white mb-8">
            <motion.h1 
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="text-5xl md:text-7xl font-bold mb-4 bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent"
            >
              Discover Events
            </motion.h1>
            <motion.p 
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4, duration: 0.6 }}
              className="text-xl md:text-2xl text-blue-100 mb-8 max-w-2xl mx-auto"
            >
              Connect with your community through amazing local events
            </motion.p>
          </div>

          {/* Search Bar */}
          <motion.div 
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="max-w-4xl mx-auto"
          >
            <div className="flex flex-col md:flex-row gap-4 p-2 bg-white/10 backdrop-blur-lg rounded-2xl border border-white/20">
              <div className="flex-1 relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/60 h-5 w-5" />
                <input
                  type="text"
                  placeholder="Search events..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 bg-transparent text-white placeholder-white/60 text-lg focus:outline-none"
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedState}
                  onChange={(e) => {
                    setSelectedState(e.target.value);
                    setSelectedCity("");
                  }}
                  aria-label="Select state"
                  className="px-4 py-4 bg-white/10 text-white rounded-xl border border-white/20 focus:outline-none focus:ring-2 focus:ring-white/30"
                >
                  <option value="">All States</option>
                  {states.map(state => (
                    <option key={state.id} value={state.id} className="text-gray-800">
                      {state.name}
                    </option>
                  ))}
                </select>
                {session && (
                  <Link href="/events/create">
                    <motion.button 
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                      className="px-8 py-4 bg-gradient-to-r from-pink-500 to-violet-500 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center gap-2"
                    >
                      <Plus className="h-5 w-5" />
                      Create Event
                    </motion.button>
                  </Link>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      </motion.section>

      {/* Featured Events Carousel */}
      {featuredEvents.length > 0 && (
        <motion.section 
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8, duration: 0.6 }}
          className="py-16"
        >
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12 bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              Featured Events
            </h2>
            <div className="relative max-w-6xl mx-auto">
              <div className="overflow-hidden rounded-2xl">
                <motion.div 
                  className="flex transition-transform duration-500"
                  style={{ transform: `translateX(-${currentSlide * 100}%)` }}
                >
                  {featuredEvents.map((event) => (
                    <div key={event.id} className="w-full flex-shrink-0">
                      <div className="grid md:grid-cols-2 gap-8 items-center p-8 bg-white/60 backdrop-blur-lg border border-white/20">
                        <div>
                          <span className="inline-block px-3 py-1 bg-gradient-to-r from-purple-100 to-blue-100 text-purple-700 rounded-full text-sm font-medium mb-4">
                            {event.category}
                          </span>
                          <h3 className="text-3xl font-bold text-gray-900 mb-4">{event.title}</h3>
                          <p className="text-gray-600 mb-6 line-clamp-3">{event.description}</p>
                          <div className="flex items-center gap-6 text-sm text-gray-500 mb-6">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4" />
                              {new Date(event.date).toLocaleDateString()}
                            </div>
                            <div className="flex items-center gap-2">
                              <MapPin className="h-4 w-4" />
                              {event.city}, {event.state}
                            </div>
                            <div className="flex items-center gap-2">
                              <Users className="h-4 w-4" />
                              {event._count.rsvps} interested
                            </div>
                          </div>
                          <Link href={`/events/${event.id}`}>
                            <motion.button 
                              whileHover={{ scale: 1.05 }}
                              className="px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                            >
                              View Details
                            </motion.button>
                          </Link>
                        </div>
                        <div className="relative h-64 md:h-80">
                          {event.image ? (
                            <Image
                              src={event.image}
                              alt={event.title}
                              fill
                              className="object-cover rounded-xl"
                            />
                          ) : (
                            <div className="w-full h-full bg-gradient-to-br from-purple-200 to-blue-200 rounded-xl flex items-center justify-center">
                              <Calendar className="h-16 w-16 text-purple-500" />
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </motion.div>
              </div>
              
              {/* Carousel Controls */}
              <button 
                onClick={prevSlide}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 p-3 bg-white/80 backdrop-blur-lg rounded-full shadow-lg hover:bg-white transition-all duration-300"
              >
                <ChevronLeft className="h-6 w-6 text-gray-700" />
              </button>
              <button 
                onClick={nextSlide}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 p-3 bg-white/80 backdrop-blur-lg rounded-full shadow-lg hover:bg-white transition-all duration-300"
              >
                <ChevronRight className="h-6 w-6 text-gray-700" />
              </button>
              
              {/* Carousel Indicators */}
              <div className="flex justify-center gap-2 mt-6">
                {featuredEvents.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentSlide(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      currentSlide === index 
                        ? 'bg-purple-600 w-8' 
                        : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </motion.section>
      )}

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        
        {/* Filters and View Toggle */}
        <div className="flex flex-col lg:flex-row gap-6">
          
          {/* Sidebar Filters */}
          <motion.div 
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 1, duration: 0.6 }}
            className={`lg:w-80 ${showFilters ? 'block' : 'hidden lg:block'}`}
          >
            <div className="bg-white/60 backdrop-blur-lg rounded-2xl p-6 border border-white/20 sticky top-8">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">Filters</h3>
                <button 
                  onClick={() => setShowFilters(false)}
                  className="lg:hidden p-2 hover:bg-gray-100 rounded-lg"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              {/* Category Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white/80 text-gray-900"
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Location Filters */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                <div className="space-y-3">
                  <select
                    value={selectedState}
                    onChange={(e) => {
                      setSelectedState(e.target.value);
                      setSelectedCity("");
                    }}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white/80 text-gray-900"
                  >
                    <option value="">All States</option>
                    {states.map(state => (
                      <option key={state.id} value={state.id}>{state.name}</option>
                    ))}
                  </select>
                  
                  {selectedState && (
                    <select
                      value={selectedCity}
                      onChange={(e) => setSelectedCity(e.target.value)}
                      className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white/80 text-gray-900"
                    >
                      <option value="">All Cities</option>
                      {filteredCities.map(city => (
                        <option key={city.id} value={city.id}>{city.name}</option>
                      ))}
                    </select>
                  )}
                </div>
              </div>

              {/* Price Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Price</label>
                <div className="space-y-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={freeOnly}
                      onChange={(e) => setFreeOnly(e.target.checked)}
                      className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Free Events</span>
                  </label>
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={paidOnly}
                      onChange={(e) => setPaidOnly(e.target.checked)}
                      className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">Paid Events</span>
                  </label>
                </div>
              </div>

              {/* Sort Filter */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 bg-white/80 text-gray-900"
                >
                  <option value="date">Date</option>
                  <option value="newest">Newest</option>
                  <option value="popular">Most Popular</option>
                </select>
              </div>
            </div>
          </motion.div>

          {/* Main Content Area */}
          <div className="flex-1">
            
            {/* View Toggle and Mobile Filters */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center gap-4">
                <button 
                  onClick={() => setShowFilters(true)}
                  className="lg:hidden flex items-center gap-2 px-4 py-2 bg-white/60 backdrop-blur-lg rounded-xl border border-white/20 hover:bg-white/80 transition-all duration-300"
                >
                  <Filter className="h-5 w-5" />
                  Filters
                </button>
                <span className="text-gray-600">{events.length} events found</span>
              </div>
              
              <div className="flex items-center gap-2 bg-white/60 backdrop-blur-lg rounded-xl p-1 border border-white/20">
                <button
                  onClick={() => setViewMode("grid")}
                  className={`p-3 rounded-lg transition-all duration-300 ${
                    viewMode === "grid" 
                      ? 'bg-white shadow-md text-purple-600' 
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Grid className="h-5 w-5" />
                </button>
                <button
                  onClick={() => setViewMode("map")}
                  className={`p-3 rounded-lg transition-all duration-300 ${
                    viewMode === "map" 
                      ? 'bg-white shadow-md text-purple-600' 
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <Map className="h-5 w-5" />
                </button>
              </div>
            </div>

            {/* Content Display */}
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600"></div>
              </div>
            ) : viewMode === "grid" ? (
              /* Events Grid */
              <motion.div 
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2, duration: 0.6 }}
                className="grid md:grid-cols-2 xl:grid-cols-3 gap-6"
              >
                {events.map((event, index) => (
                  <motion.div
                    key={event.id}
                    initial={{ opacity: 0, y: 50 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1.4 + index * 0.1, duration: 0.6 }}
                    whileHover={{ y: -8, scale: 1.02 }}
                    className="group bg-white/70 backdrop-blur-lg rounded-2xl border border-white/30 overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500"
                  >
                    {/* Event Image */}
                    <div className="relative h-48 overflow-hidden">
                      {event.image ? (
                        <Image
                          src={event.image}
                          alt={event.title}
                          fill
                          className="object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-purple-200 via-blue-200 to-teal-200 flex items-center justify-center group-hover:from-purple-300 group-hover:via-blue-300 group-hover:to-teal-300 transition-all duration-500">
                          <Calendar className="h-12 w-12 text-purple-600" />
                        </div>
                      )}
                      
                      {/* Featured Badge */}
                      {event.isFeatured && (
                        <div className="absolute top-4 left-4 px-3 py-1 bg-gradient-to-r from-pink-500 to-violet-500 text-white text-xs font-medium rounded-full">
                          Featured
                        </div>
                      )}
                      
                      {/* Price Badge */}
                      <div className="absolute top-4 right-4 px-3 py-1 bg-black/60 backdrop-blur-lg text-white text-xs font-medium rounded-full">
                        {event.price ? `₹${event.price}` : 'Free'}
                      </div>
                    </div>

                    {/* Event Content */}
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-3">
                        <span className="inline-block px-3 py-1 bg-gradient-to-r from-purple-100 to-blue-100 text-purple-700 rounded-full text-xs font-medium">
                          {event.category}
                        </span>
                        <div className="flex items-center gap-1 text-gray-500 text-sm">
                          <Users className="h-4 w-4" />
                          {event._count.rsvps}
                        </div>
                      </div>
                      
                      <h3 className="text-xl font-bold text-gray-900 mb-2 line-clamp-2 group-hover:text-purple-600 transition-colors duration-300">
                        {event.title}
                      </h3>
                      
                      <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                        {event.description}
                      </p>
                      
                      <div className="space-y-2 mb-6">
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Calendar className="h-4 w-4" />
                          {new Date(event.date).toLocaleDateString('en-US', {
                            weekday: 'short',
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric'
                          })}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <MapPin className="h-4 w-4" />
                          {event.city}, {event.state}
                        </div>
                        <div className="flex items-center gap-2 text-sm text-gray-500">
                          <Clock className="h-4 w-4" />
                          {new Date(event.date).toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </div>
                      
                      {/* Action Buttons */}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            animate={heartAnimations.has(event.id) ? {
                              scale: [1, 1.5, 1.2, 1],
                              rotate: [0, -10, 10, 0]
                            } : {}}
                            transition={{ duration: 0.6, ease: "easeOut" }}
                            onClick={() => handleRSVP(event.id)}
                            className={`relative p-2 rounded-lg transition-all duration-300 ${
                              likedEvents.has(event.id)
                                ? 'bg-gradient-to-r from-pink-500 to-red-500 text-white shadow-lg'
                                : 'bg-gradient-to-r from-gray-200 to-gray-300 text-gray-600 hover:from-pink-500 hover:to-red-500 hover:text-white hover:shadow-lg'
                            }`}
                            title={likedEvents.has(event.id) ? 'Remove from favorites' : 'Add to favorites'}
                          >
                            <Heart 
                              className={`h-4 w-4 transition-all duration-300 ${
                                likedEvents.has(event.id) ? 'fill-current' : ''
                              }`} 
                            />
                            {heartAnimations.has(event.id) && (
                              <motion.div
                                initial={{ scale: 0, opacity: 1 }}
                                animate={{ scale: 2, opacity: 0 }}
                                transition={{ duration: 0.6 }}
                                className="absolute inset-0 flex items-center justify-center pointer-events-none"
                              >
                                <Heart className={`h-4 w-4 fill-current ${
                                  likedEvents.has(event.id) ? 'text-pink-400' : 'text-gray-400'
                                }`} />
                              </motion.div>
                            )}
                          </motion.button>
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            onClick={() => handleShare(event)}
                            className="p-2 bg-gradient-to-r from-blue-500 to-teal-500 text-white rounded-lg hover:shadow-lg transition-all duration-300"
                          >
                            <Share2 className="h-4 w-4" />
                          </motion.button>
                        </div>
                        
                        <Link href={`/events/${event.id}`}>
                          <motion.button 
                            whileHover={{ scale: 1.05 }}
                            className="px-6 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-300"
                          >
                            View Details
                          </motion.button>
                        </Link>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              /* Map View */
              <motion.div 
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.2, duration: 0.6 }}
                className="h-[600px] rounded-2xl overflow-hidden border border-white/30 shadow-lg"
              >
                <EventsMap events={events} onEventSelect={(event: Event) => setSelectedEvent(event)} />
              </motion.div>
            )}

            {/* No Events Found */}
            {!loading && events.length === 0 && (
              <motion.div 
                initial={{ opacity: 0, y: 50 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2, duration: 0.6 }}
                className="text-center py-16"
              >
                <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-gray-600 mb-2">No events found</h3>
                <p className="text-gray-500 mb-6">Try adjusting your filters or search terms</p>
                {session && (
                  <Link href="/events/create">
                    <motion.button 
                      whileHover={{ scale: 1.05 }}
                      className="px-8 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      Create the First Event
                    </motion.button>
                  </Link>
                )}
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Event Detail Modal */}
      <AnimatePresence>
        {selectedEvent && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-[9999] p-4"
            onClick={() => setSelectedEvent(null)}
          >
            <motion.div
              initial={{ scale: 0.9, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.9, opacity: 0 }}
              className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto relative z-[10000]"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="relative">
                {selectedEvent.image ? (
                  <Image
                    src={selectedEvent.image}
                    alt={selectedEvent.title}
                    width={800}
                    height={300}
                    className="w-full h-64 object-cover"
                  />
                ) : (
                  <div className="w-full h-64 bg-gradient-to-br from-purple-200 to-blue-200 flex items-center justify-center">
                    <Calendar className="h-16 w-16 text-purple-600" />
                  </div>
                )}
                <button
                  onClick={() => setSelectedEvent(null)}
                  className="absolute top-4 right-4 p-2 bg-black/60 backdrop-blur-lg text-white rounded-full hover:bg-black/80 transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
              
              <div className="p-8">
                <div className="flex items-center justify-between mb-4">
                  <span className="inline-block px-3 py-1 bg-gradient-to-r from-purple-100 to-blue-100 text-purple-700 rounded-full text-sm font-medium">
                    {selectedEvent.category}
                  </span>
                  <div className="text-2xl font-bold text-purple-600">
                    {selectedEvent.price ? `₹${selectedEvent.price}` : 'Free'}
                  </div>
                </div>
                
                <h2 className="text-3xl font-bold text-gray-900 mb-4">{selectedEvent.title}</h2>
                <p className="text-gray-600 mb-6">{selectedEvent.description}</p>
                
                <div className="grid md:grid-cols-2 gap-6 mb-8">
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-5 w-5 text-purple-600" />
                      <div>
                        <div className="font-medium text-gray-900">
                          {new Date(selectedEvent.date).toLocaleDateString('en-US', {
                            weekday: 'long',
                            month: 'long',
                            day: 'numeric',
                            year: 'numeric'
                          })}
                        </div>
                        <div className="text-sm text-gray-500">
                          {new Date(selectedEvent.date).toLocaleTimeString('en-US', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <MapPin className="h-5 w-5 text-purple-600" />
                      <div>
                        <div className="font-medium text-gray-900">{selectedEvent.location}</div>
                        <div className="text-sm text-gray-500">{selectedEvent.city}, {selectedEvent.state}</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <Users className="h-5 w-5 text-purple-600" />
                      <div>
                        <div className="font-medium text-gray-900">{selectedEvent._count.rsvps} people interested</div>
                        <div className="text-sm text-gray-500">Join them!</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-3">
                      <Eye className="h-5 w-5 text-purple-600" />
                      <div>
                        <div className="font-medium text-gray-900">Organized by</div>
                        <div className="text-sm text-gray-500">{selectedEvent.createdBy.name}</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex gap-4">
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    onClick={() => handleRSVP(selectedEvent.id, 'GOING')}
                    className="flex-1 px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
                  >
                    I&apos;m Going
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    onClick={() => handleRSVP(selectedEvent.id, 'INTERESTED')}
                    className="flex-1 px-6 py-3 border-2 border-purple-600 text-purple-600 font-semibold rounded-xl hover:bg-purple-50 transition-all duration-300"
                  >
                    Interested
                  </motion.button>
                  <motion.button
                    whileHover={{ scale: 1.1 }}
                    onClick={() => handleShare(selectedEvent)}
                    className="px-6 py-3 bg-gray-100 text-gray-700 rounded-xl hover:bg-gray-200 transition-all duration-300"
                  >
                    <Share2 className="h-5 w-5" />
                  </motion.button>
                </div>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Mobile Filter Overlay */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="lg:hidden fixed inset-0 bg-black/60 backdrop-blur-sm z-50"
            onClick={() => setShowFilters(false)}
          >
            <motion.div
              initial={{ x: -300 }}
              animate={{ x: 0 }}
              exit={{ x: -300 }}
              className="w-80 h-full bg-white shadow-xl overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Mobile filters content - same as sidebar */}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Toast Notification */}
      <AnimatePresence>
        {toast && (
          <motion.div
            initial={{ opacity: 0, y: -50, scale: 0.9 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -50, scale: 0.9 }}
            className="fixed top-4 right-4 z-[10001] max-w-sm"
          >
            <div className={`px-6 py-4 rounded-xl shadow-lg backdrop-blur-lg border ${
              toast.type === 'success' 
                ? 'bg-green-50/90 border-green-200 text-green-800' 
                : 'bg-red-50/90 border-red-200 text-red-800'
            }`}>
              <div className="flex items-center gap-3">
                <div className={`w-2 h-2 rounded-full ${
                  toast.type === 'success' ? 'bg-green-500' : 'bg-red-500'
                }`}></div>
                <p className="font-medium">{toast.message}</p>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
