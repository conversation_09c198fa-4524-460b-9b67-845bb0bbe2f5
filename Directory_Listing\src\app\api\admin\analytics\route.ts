import { NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";
import { requireAdmin } from "@/lib/auth-middleware";

export async function GET() {
  const authError = await requireAdmin();
  if (authError) return authError;

  try {
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // Get total counts
    const totalListings = await prisma.listing.count();
    const totalUsers = await prisma.user.count();
    const totalCategories = await prisma.category.count();
    const totalTags = await prisma.tag.count();

    // Get listings by status
    const listingsByStatus = await prisma.listing.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    });

    // Get new users in last 7 days
    const newUsers = await prisma.user.count({
      where: {
        createdAt: {
          gte: sevenDaysAgo
        }
      }
    });

    // Get most popular categories
    const popularCategories = await prisma.category.findMany({
      include: {
        _count: {
          select: {
            listings: true
          }
        }
      },
      orderBy: {
        listings: {
          _count: 'desc'
        }
      },
      take: 5
    });

    // Get most viewed listings
    const mostViewedListings = await prisma.listing.findMany({
      where: {
        status: 'APPROVED'
      },
      include: {
        category: {
          select: {
            name: true
          }
        },
        user: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        views: 'desc'
      },
      take: 5
    });

    // Get recent activity
    const recentListings = await prisma.listing.findMany({
      include: {
        category: {
          select: {
            name: true
          }
        },
        user: {
          select: {
            name: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });

    const analytics = {
      totalListings,
      totalUsers,
      totalCategories,
      totalTags,
      listingsByStatus: listingsByStatus.reduce((acc, item) => {
        acc[item.status] = item._count.id;
        return acc;
      }, {} as Record<string, number>),
      newUsers,
      popularCategories,
      mostViewedListings,
      recentListings
    };

    return NextResponse.json(analytics);
  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}
