const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function createIndianListings() {
  try {
    // Get the test user
    const user = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!user) {
      console.log('❌ Test user not found. Please run create-test-user.js first');
      return;
    }

    // Get categories
    const restaurantCategory = await prisma.category.findUnique({
      where: { name: 'Restaurants' }
    });
    const cafeCategory = await prisma.category.findUnique({
      where: { name: 'Cafes & Bakeries' }
    });
    const fitnessCategory = await prisma.category.findUnique({
      where: { name: 'Fitness & Yoga' }
    });

    // Get some tags
    const vegetarianTag = await prisma.tag.findUnique({
      where: { name: 'Vegetarian' }
    });
    const pureVegTag = await prisma.tag.findUnique({
      where: { name: 'Pure Veg' }
    });
    const wifiTag = await prisma.tag.findUnique({
      where: { name: 'WiFi' }
    });
    const parkingTag = await prisma.tag.findUnique({
      where: { name: 'Parking Available' }
    });

    // Sample Indian listings
    const listings = [
      {
        title: 'Saravana Bhavan',
        description: 'Authentic South Indian vegetarian restaurant known for its delicious dosas, idlis, and traditional Tamil Nadu cuisine. Family-friendly atmosphere with excellent service.',
        address: 'Shop No. 15, Express Avenue Mall',
        city: 'Chennai',
        state: 'Tamil Nadu',
        country: 'India',
        pincode: '600002',
        phone: '+91 98765 43210',
        email: '<EMAIL>',
        website: 'https://www.saravanabhavan.in',
        categoryId: restaurantCategory?.id,
        userId: user.id,
        tags: {
          connect: [
            { id: vegetarianTag?.id },
            { id: pureVegTag?.id },
            { id: wifiTag?.id },
            { id: parkingTag?.id }
          ].filter(Boolean)
        }
      },
      {
        title: 'Cafe Coffee Day - Connaught Place',
        description: 'Popular coffee chain serving freshly brewed coffee, snacks, and light meals. Great place to hang out with friends or work on your laptop.',
        address: 'A-12, Connaught Place',
        city: 'Delhi',
        state: 'Delhi',
        country: 'India',
        pincode: '110001',
        phone: '+91 98765 43211',
        email: '<EMAIL>',
        website: 'https://www.cafecoffeeday.com',
        categoryId: cafeCategory?.id,
        userId: user.id,
        tags: {
          connect: [
            { id: wifiTag?.id },
            { id: parkingTag?.id }
          ].filter(Boolean)
        }
      },
      {
        title: 'The Yoga Institute',
        description: 'One of India\'s oldest yoga institutes offering traditional Hatha Yoga classes, meditation sessions, and teacher training programs.',
        address: 'Prabhat Colony, Santacruz East',
        city: 'Mumbai',
        state: 'Maharashtra',
        country: 'India',
        pincode: '400055',
        phone: '+91 98765 43212',
        email: '<EMAIL>',
        website: 'https://www.theyogainstitute.org',
        categoryId: fitnessCategory?.id,
        userId: user.id,
        tags: {
          connect: [
            { id: parkingTag?.id }
          ].filter(Boolean)
        }
      },
      {
        title: 'Karim\'s Restaurant',
        description: 'Historic Mughlai restaurant established in 1913, famous for its kebabs, biryanis, and traditional North Indian non-vegetarian cuisine.',
        address: '16, Gali Kababian, Jama Masjid',
        city: 'Delhi',
        state: 'Delhi',
        country: 'India',
        pincode: '110006',
        phone: '+91 98765 43213',
        email: '<EMAIL>',
        website: 'https://www.karims.in',
        categoryId: restaurantCategory?.id,
        userId: user.id,
        tags: {
          connect: [
            { id: parkingTag?.id }
          ].filter(Boolean)
        }
      },
      {
        title: 'Mysore Pak Sweet Shop',
        description: 'Traditional South Indian sweet shop specializing in Mysore Pak, along with other authentic Karnataka sweets and snacks.',
        address: '123, Gandhi Bazaar',
        city: 'Bangalore',
        state: 'Karnataka',
        country: 'India',
        pincode: '560004',
        phone: '+91 98765 43214',
        email: '<EMAIL>',
        categoryId: cafeCategory?.id,
        userId: user.id,
        tags: {
          connect: [
            { id: vegetarianTag?.id },
            { id: pureVegTag?.id }
          ].filter(Boolean)
        }
      }
    ];

    // Create listings
    for (const listing of listings) {
      const created = await prisma.listing.create({
        data: listing,
        include: {
          category: true,
          tags: true
        }
      });
      console.log(`✅ Created listing: ${created.title}`);
    }

    console.log(`✅ Successfully created ${listings.length} sample Indian listings!`);
  } catch (error) {
    console.error('❌ Error creating Indian listings:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createIndianListings();
