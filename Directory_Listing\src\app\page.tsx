"use client";
import { useState, useEffect } from "react";
import Link from "next/link";
import Image from "next/image";
import LocationIndicator from "@/components/LocationIndicator";
import { Event } from "@/types/events";

interface Listing {
  id: string;
  title: string;
  description: string;
  address: string;
  city: string;
  state: string;
  category: {
    id: string;
    name: string;
  };
  tags: Array<{
    id: string;
    name: string;
  }>;
  images: Array<{
    id: string;
    url: string;
  }>;
  user: {
    id: string;
    name?: string;
  };
  createdAt: string;
}

const testimonials = [
  { name: "<PERSON><PERSON>", role: "Business Owner", text: "CityDirectory helped my restaurant get discovered by hundreds of new customers across Mumbai!", avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?auto=format&fit=crop&w=150&q=80" },
  { name: "<PERSON><PERSON>hta", role: "Event Organizer", text: "The platform made promoting our cultural events incredibly easy and effective in Delhi.", avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=150&q=80" },
];

export default function Home() {
  const [search, setSearch] = useState("");
  const [featuredListings, setFeaturedListings] = useState<Listing[]>([]);
  const [featuredEvents, setFeaturedEvents] = useState<Event[]>([]);
  const [categories, setCategories] = useState([
    { name: "Restaurants", icon: "🍽️", color: "from-amber-400 to-orange-500", count: "0" },
    { name: "Events", icon: "🎉", color: "from-blue-500 to-indigo-600", count: "0" },
    { name: "Fitness", icon: "🏋️", color: "from-emerald-400 to-teal-500", count: "0" },
    { name: "Shopping", icon: "🛍️", color: "from-pink-400 to-rose-500", count: "0" },
    { name: "Services", icon: "🛠️", color: "from-purple-400 to-violet-500", count: "0" },
    { name: "Nightlife", icon: "🍸", color: "from-indigo-500 to-purple-600", count: "0" },
  ]);
  const [stats, setStats] = useState([
    { number: "0", label: "Active Businesses" },
    { number: "0", label: "Categories" },
    { number: "0", label: "Cities Covered" },
    { number: "4.9", label: "User Rating" },
  ]);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    async function fetchData() {
      try {
        const [listingsResponse, categoriesResponse, eventsResponse] = await Promise.all([
          fetch('/api/listings'),
          fetch('/api/admin/categories'),
          fetch('/api/events')
        ]);
        
        const allListings = await listingsResponse.json();
        const allCategories = await categoriesResponse.json();
        const allEvents = await eventsResponse.json();
        
        // Randomly select 3 listings for featured section
        const shuffled = allListings.sort(() => 0.5 - Math.random());
        const featured = shuffled.slice(0, 3);

        // Randomly select 2 approved events for featured section
        const approvedEvents = allEvents.events?.filter((event: Event) => event.status === 'APPROVED') || [];
        const shuffledEvents = approvedEvents.sort(() => 0.5 - Math.random());
        const featuredEventsSlice = shuffledEvents.slice(0, 2);
        
        // Update category counts based on actual data
        const categoryCountMap = new Map();
        const citiesSet = new Set();
        
        allListings.forEach((listing: Listing) => {
          const categoryName = listing.category.name;
          categoryCountMap.set(categoryName, (categoryCountMap.get(categoryName) || 0) + 1);
          citiesSet.add(listing.city);
        });
        
        // Update categories with real counts
        const updatedCategories = categories.map(cat => ({
          ...cat,
          count: categoryCountMap.get(cat.name) ? `${categoryCountMap.get(cat.name)}` : "0"
        }));
        
        // Update stats with real data
        const updatedStats = [
          { number: `${allListings.length}+`, label: "Active Businesses" },
          { number: `${allCategories.length}+`, label: "Categories" },
          { number: `${citiesSet.size}+`, label: "Cities Covered" },
          { number: "4.9", label: "User Rating" },
        ];
        
        setFeaturedListings(featured);
        setFeaturedEvents(featuredEventsSlice);
        setCategories(updatedCategories);
        setStats(updatedStats);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    }
    
    fetchData();
  }, []);
  
  return (
    <div className="relative">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
        {/* Background with overlay */}
        <div className="absolute inset-0 z-0">
          <Image 
            src="https://images.unsplash.com/photo-1449824913935-59a10b8d2000?auto=format&fit=crop&w=1920&q=80" 
            alt="Modern cityscape" 
            fill 
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-gradient-to-br from-slate-900/80 via-blue-900/70 to-indigo-900/80" />
        </div>
        
        {/* Hero Content */}
        <div className="relative z-10 container mx-auto px-6 lg:px-8 text-center">
          <div className="max-w-4xl mx-auto space-y-8 animate-fade-in-up">
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-extrabold text-white leading-tight">
              Discover India&apos;s 
              <span className="block bg-gradient-to-r from-blue-400 to-indigo-400 bg-clip-text text-transparent">
                Best Businesses
              </span>
            </h1>
            
            {/* Location Indicator */}
            <div className="flex justify-center">
              <LocationIndicator />
            </div>
            
            <p className="text-xl md:text-2xl text-blue-100 max-w-2xl mx-auto leading-relaxed">
              Connect with exceptional local businesses across India, discover amazing services, and explore hidden gems in your city - all in one beautiful platform.
            </p>
            
            {/* Search Bar */}
            <div className="max-w-2xl mx-auto">
              <form 
                className="relative group"
                onSubmit={e => { 
                  e.preventDefault(); 
                  window.location.href = `/listings?search=${search}`; 
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl blur opacity-75 group-hover:opacity-100 transition duration-300"></div>
                <div className="relative flex bg-white rounded-2xl p-2 shadow-2xl">
                  <input 
                    value={search} 
                    onChange={e => setSearch(e.target.value)}
                    className="flex-1 px-6 py-4 text-lg bg-transparent border-none focus:outline-none placeholder-slate-500 text-gray-900 bg-white"
                    placeholder="Search restaurants, gyms, events, or anything in India..."
                  />
                  <button 
                    type="submit"
                    className="px-8 py-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white font-semibold rounded-xl hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl"
                  >
                    Search
                  </button>
                </div>
              </form>
            </div>
            
            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-3xl mx-auto pt-8">
              {stats.map((stat, index) => (
                <div key={index} className="text-center animate-fade-in delay-300">
                  <div className="text-3xl md:text-4xl font-bold text-white">{stat.number}</div>
                  <div className="text-blue-200 text-sm md:text-base">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2"></div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="container mx-auto px-6 lg:px-8 py-20 space-y-32">
        
        {/* Featured Listings */}
        <section className="animate-fade-in">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">Featured Businesses</h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">Discover top-rated businesses that our community loves</p>
          </div>
          
          {loading ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {featuredListings.map((listing, index) => {
                const primaryImage = listing.images?.[0]?.url || "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?auto=format&fit=crop&w=600&q=80";
                const randomRating = (4.2 + Math.random() * 0.7).toFixed(1); // Random rating between 4.2-4.9
                
                return (
                  <div 
                    key={listing.id} 
                    className="group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden animate-slide-up"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className="relative h-64 overflow-hidden">
                      <Image 
                        src={primaryImage} 
                        alt={listing.title} 
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-500"
                      />
                      <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full">
                        <span className="text-yellow-500">★</span>
                        <span className="text-slate-900 font-semibold ml-1">{randomRating}</span>
                      </div>
                    </div>
                    
                    <div className="p-6">
                      <h3 className="text-xl font-bold text-slate-900 mb-2">{listing.title}</h3>
                      <p className="text-slate-600 mb-4">{listing.category.name} • {listing.city}</p>
                      <Link 
                        href={`/listings/${listing.id}`} 
                        className="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold group-hover:translate-x-1 transition-transform duration-200"
                      >
                        View Details →
                      </Link>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </section>

        {/* Browse by Category */}
        <section className="animate-fade-in">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">Explore Categories</h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">Find exactly what you&apos;re looking for</p>
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {categories.map((category, index) => (
              <Link 
                key={category.name}
                href={`/listings?category=${category.name}`}
                className="group relative p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 text-center animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className={`absolute inset-0 bg-gradient-to-br ${category.color} opacity-0 group-hover:opacity-10 rounded-2xl transition-opacity duration-300`}></div>
                
                <div className="relative">
                  <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-200">
                    {category.icon}
                  </div>
                  <h3 className="font-bold text-slate-900 mb-2">{category.name}</h3>
                  <p className="text-slate-500 text-sm">{category.count}</p>
                </div>
              </Link>
            ))}
          </div>
        </section>
        {/* Featured Events */}
        <section className="animate-fade-in">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">Upcoming Events</h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">Don&apos;t miss out on exciting local events</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {featuredEvents.map((event, index) => (
              <div 
                key={event.id} 
                className="group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="relative h-48 overflow-hidden">
                  <Image 
                    src={event.image || 'https://images.unsplash.com/photo-1492684223066-81342ee5ff30?auto=format&fit=crop&w=600&q=80'} 
                    alt={event.title} 
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-2 rounded-xl">
                    <div className="text-sm font-bold text-slate-900">
                      {new Date(event.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                    </div>
                  </div>
                </div>
                
                <div className="p-6">
                  <h3 className="text-xl font-bold text-slate-900 mb-2">{event.title}</h3>
                  <p className="text-slate-600 mb-4">{event.city} • {new Date(event.date).toLocaleDateString()}</p>
                  <Link 
                    href={`/events/${event.id}`} 
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 font-semibold group-hover:translate-x-1 transition-transform duration-200"
                  >
                    View Event →
                  </Link>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* How It Works */}
        <section className="relative">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-3xl"></div>
          <div className="relative p-12 lg:p-20">
            <div className="text-center mb-16">
              <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">How It Works</h2>
              <p className="text-xl text-slate-600 max-w-2xl mx-auto">Getting started is simple and intuitive</p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-5xl mx-auto">
              <div className="text-center animate-slide-up">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center text-white text-2xl font-bold mx-auto mb-6 shadow-lg">
                  1
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-4">Search & Discover</h3>
                <p className="text-slate-600 leading-relaxed">Browse through thousands of businesses and events, or search for exactly what you need.</p>
              </div>
              
              <div className="text-center animate-slide-up delay-100">
                <div className="w-20 h-20 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center text-white text-2xl font-bold mx-auto mb-6 shadow-lg">
                  2
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-4">Explore Details</h3>
                <p className="text-slate-600 leading-relaxed">Get comprehensive information including photos, reviews, contact details, and location maps.</p>
              </div>
              
              <div className="text-center animate-slide-up delay-200">
                <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center text-white text-2xl font-bold mx-auto mb-6 shadow-lg">
                  3
                </div>
                <h3 className="text-xl font-bold text-slate-900 mb-4">Connect & Visit</h3>
                <p className="text-slate-600 leading-relaxed">Connect directly with businesses, book appointments, and enjoy amazing local experiences.</p>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonials */}
        <section className="animate-fade-in">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">What People Say</h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">Hear from our satisfied community members</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <div 
                key={testimonial.name} 
                className="bg-white rounded-3xl shadow-lg p-8 animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-center mb-6">
                  <Image 
                    src={testimonial.avatar} 
                    alt={testimonial.name}
                    width={60}
                    height={60}
                    className="rounded-full object-cover"
                  />
                  <div className="ml-4">
                    <h4 className="font-bold text-slate-900">{testimonial.name}</h4>
                    <p className="text-slate-600 text-sm">{testimonial.role}</p>
                  </div>
                </div>
                <p className="text-slate-700 leading-relaxed italic">&quot;{testimonial.text}&quot;</p>
                <div className="flex text-yellow-400 mt-4">
                  {[...Array(5)].map((_, i) => (
                    <span key={i}>★</span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Blog Preview */}
        <section className="animate-fade-in">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900 mb-4">Latest Insights</h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">Stay updated with local trends and business tips</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <article className="bg-white rounded-3xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 animate-slide-up">
              <div className="relative h-48">
                <Image 
                  src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?auto=format&fit=crop&w=600&q=80" 
                  alt="Business insights"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-slate-900 mb-3">5 Must-Visit Hidden Gems This Summer</h3>
                <p className="text-slate-600 mb-4 leading-relaxed">Discover amazing local spots that only the locals know about. From cozy cafes to unique shops...</p>
                <Link href="/blog/1" className="text-blue-600 hover:text-blue-700 font-semibold">Read More →</Link>
              </div>
            </article>
            
            <article className="bg-white rounded-3xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-300 animate-slide-up delay-100">
              <div className="relative h-48">
                <Image 
                  src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=crop&w=600&q=80" 
                  alt="Business growth"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="text-xl font-bold text-slate-900 mb-3">How to List Your Business Successfully</h3>
                <p className="text-slate-600 mb-4 leading-relaxed">A comprehensive guide to creating an outstanding business listing that attracts customers...</p>
                <Link href="/blog/2" className="text-blue-600 hover:text-blue-700 font-semibold">Read More →</Link>
              </div>
            </article>
          </div>
        </section>

        {/* Call to Action */}
        <section className="relative text-center py-20">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-600 to-indigo-700 rounded-3xl"></div>
          <div className="relative text-white">
            <h2 className="text-4xl md:text-5xl font-bold mb-6">Ready to Get Discovered?</h2>
            <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto leading-relaxed">
              Join thousands of businesses already thriving on CityDirectory
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                href="/add-listing" 
                className="px-8 py-4 bg-white text-blue-600 font-bold rounded-xl hover:bg-blue-50 transition-all duration-200 shadow-lg hover:shadow-xl"
              >
                List Your Business
              </Link>
              <Link 
                href="/listings" 
                className="px-8 py-4 border-2 border-white text-white font-bold rounded-xl hover:bg-white hover:text-blue-600 transition-all duration-200"
              >
                Explore Listings
              </Link>
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
